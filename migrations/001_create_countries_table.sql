-- 创建国家表
CREATE TABLE IF NOT EXISTS countries (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    flag VARCHAR(255),
    currency VARCHAR(10),
    status SMALLINT DEFAULT 1 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_countries_code ON countries(code);
CREATE INDEX IF NOT EXISTS idx_countries_status ON countries(status);
CREATE INDEX IF NOT EXISTS idx_countries_deleted_at ON countries(deleted_at);

-- 插入初始国家数据
INSERT INTO countries (id, name, code, flag, currency, status) VALUES
(1, 'United States', 'US', '🇺🇸', 'USD', 1),
(2, 'United Kingdom', 'UK', '🇬🇧', 'GBP', 1),
(3, 'Canada', 'CA', '🇨🇦', 'CAD', 1),
(4, 'Australia', 'AU', '🇦🇺', 'AUD', 1),
(5, 'Germany', 'DE', '🇩🇪', 'EUR', 1),
(6, 'France', 'FR', '🇫🇷', 'EUR', 1),
(7, 'Italy', 'IT', '🇮🇹', 'EUR', 1),
(8, 'Spain', 'ES', '🇪🇸', 'EUR', 1),
(9, 'Netherlands', 'NL', '🇳🇱', 'EUR', 1),
(10, 'Belgium', 'BE', '🇧🇪', 'EUR', 1),
(11, 'Switzerland', 'CH', '🇨🇭', 'CHF', 1),
(12, 'Austria', 'AT', '🇦🇹', 'EUR', 1),
(13, 'Sweden', 'SE', '🇸🇪', 'SEK', 1),
(14, 'Norway', 'NO', '🇳🇴', 'NOK', 1),
(15, 'Denmark', 'DK', '🇩🇰', 'DKK', 1),
(16, 'Finland', 'FI', '🇫🇮', 'EUR', 1),
(17, 'Poland', 'PL', '🇵🇱', 'PLN', 1),
(18, 'Czech Republic', 'CZ', '🇨🇿', 'CZK', 1),
(19, 'Hungary', 'HU', '🇭🇺', 'HUF', 1),
(20, 'Portugal', 'PT', '🇵🇹', 'EUR', 1),
(21, 'Ireland', 'IE', '🇮🇪', 'EUR', 1),
(22, 'Greece', 'GR', '🇬🇷', 'EUR', 1),
(23, 'Japan', 'JP', '🇯🇵', 'JPY', 1),
(24, 'South Korea', 'KR', '🇰🇷', 'KRW', 1),
(25, 'Singapore', 'SG', '🇸🇬', 'SGD', 1),
(26, 'Hong Kong', 'HK', '🇭🇰', 'HKD', 1),
(27, 'Taiwan', 'TW', '🇹🇼', 'TWD', 1),
(28, 'New Zealand', 'NZ', '🇳🇿', 'NZD', 1),
(29, 'Brazil', 'BR', '🇧🇷', 'BRL', 1),
(30, 'Mexico', 'MX', '🇲🇽', 'MXN', 1),
(31, 'India', 'IN', '🇮🇳', 'INR', 1),
(32, 'China', 'CN', '🇨🇳', 'CNY', 1),
(33, 'Russia', 'RU', '🇷🇺', 'RUB', 1),
(34, 'South Africa', 'ZA', '🇿🇦', 'ZAR', 1),
(35, 'Israel', 'IL', '🇮🇱', 'ILS', 1),
(36, 'Turkey', 'TR', '🇹🇷', 'TRY', 1),
(37, 'United Arab Emirates', 'AE', '🇦🇪', 'AED', 1),
(38, 'Saudi Arabia', 'SA', '🇸🇦', 'SAR', 1),
(39, 'Thailand', 'TH', '🇹🇭', 'THB', 1),
(40, 'Malaysia', 'MY', '🇲🇾', 'MYR', 1),
(41, 'Indonesia', 'ID', '🇮🇩', 'IDR', 1),
(42, 'Philippines', 'PH', '🇵🇭', 'PHP', 1),
(43, 'Vietnam', 'VN', '🇻🇳', 'VND', 1),
(44, 'Argentina', 'AR', '🇦🇷', 'ARS', 1),
(45, 'Chile', 'CL', '🇨🇱', 'CLP', 1),
(46, 'Colombia', 'CO', '🇨🇴', 'COP', 1),
(47, 'Peru', 'PE', '🇵🇪', 'PEN', 1),
(48, 'Egypt', 'EG', '🇪🇬', 'EGP', 1),
(49, 'Nigeria', 'NG', '🇳🇬', 'NGN', 1),
(50, 'Kenya', 'KE', '🇰🇪', 'KES', 1)
ON CONFLICT (code) DO NOTHING;

-- 更新序列值
SELECT setval('countries_id_seq', (SELECT MAX(id) FROM countries));

-- 添加商家表的country_id字段（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'merchants' AND column_name = 'country_id') THEN
        ALTER TABLE merchants ADD COLUMN country_id BIGINT DEFAULT 1 NOT NULL;
        
        -- 添加外键约束
        ALTER TABLE merchants ADD CONSTRAINT fk_merchants_country_id 
            FOREIGN KEY (country_id) REFERENCES countries(id);
        
        -- 创建索引
        CREATE INDEX idx_merchants_country_id ON merchants(country_id);
    END IF;
END $$;
