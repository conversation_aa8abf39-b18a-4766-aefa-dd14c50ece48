package dto

import (
	"bonusearned/domain/merchant/entity"
	"time"
)

// 请求对象

// GetCountryListReq 获取国家列表请求
type GetCountryListReq struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Search   string `json:"search" form:"search"`
	Status   *int8  `json:"status" form:"status"`
}

// CreateCountryReq 创建国家请求
type CreateCountryReq struct {
	Name     string `json:"name" binding:"required,min=2,max=100"`
	Code     string `json:"code" binding:"required,min=2,max=10"`
	Flag     string `json:"flag"`
	Currency string `json:"currency" binding:"max=10"`
}

// UpdateCountryReq 更新国家请求
type UpdateCountryReq struct {
	ID       uint64 `json:"id"`
	Name     string `json:"name" binding:"omitempty,min=2,max=100"`
	Code     string `json:"code" binding:"omitempty,min=2,max=10"`
	Flag     string `json:"flag"`
	Currency string `json:"currency" binding:"omitempty,max=10"`
	Status   *int8  `json:"status" binding:"omitempty,oneof=0 1"`
}

// 响应对象

// CountryResp 国家响应
type CountryResp struct {
	ID        uint64    `json:"id"`
	Name      string    `json:"name"`
	Code      string    `json:"code"`
	Flag      string    `json:"flag"`
	Currency  string    `json:"currency"`
	Status    int8      `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CountryListResponse 国家列表响应
type CountryListResponse struct {
	Total       int64         `json:"total"`
	Page        int           `json:"page"`
	PageSize    int           `json:"page_size"`
	CountryList []CountryResp `json:"country_list"`
}

// ToCountryResp 转换为国家响应
func ToCountryResp(country *entity.Country) CountryResp {
	return CountryResp{
		ID:        country.ID,
		Name:      country.Name,
		Code:      country.Code,
		Flag:      country.Flag,
		Currency:  country.Currency,
		Status:    country.Status,
		CreatedAt: country.CreatedAt,
		UpdatedAt: country.UpdatedAt,
	}
}

// ToCountryRespList 转换为国家响应列表
func ToCountryRespList(countries []*entity.Country) []CountryResp {
	result := make([]CountryResp, len(countries))
	for i, country := range countries {
		result[i] = ToCountryResp(country)
	}
	return result
}

// ToCountryEntity 转换为国家实体
func (req *CreateCountryReq) ToCountryEntity() *entity.Country {
	return &entity.Country{
		Name:     req.Name,
		Code:     req.Code,
		Flag:     req.Flag,
		Currency: req.Currency,
		Status:   1, // 默认启用
	}
}

// ToCountryEntity 转换为国家实体
func (req *UpdateCountryReq) ToCountryEntity() *entity.Country {
	country := &entity.Country{
		ID:       req.ID,
		Name:     req.Name,
		Code:     req.Code,
		Flag:     req.Flag,
		Currency: req.Currency,
	}
	
	if req.Status != nil {
		country.Status = *req.Status
	}
	
	return country
}
