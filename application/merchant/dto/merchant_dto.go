package dto

import (
	"bonusearned/domain/merchant/entity"
	"bonusearned/infra/constant"
	"bonusearned/infra/utils/cashbackutils"
	"time"

	"github.com/shopspring/decimal"
)

// 请求对象

type GetMerchantListReq struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"page_size" form:"page_size"`
	Search     string `json:"search" form:"search"`
	Offset     int    `json:"offset"`
	Sort       string `json:"sort" form:"sort"` // popular, random, newest, -newest, cashback_value, -cashback_value
	Featured   bool   `json:"featured" form:"featured"`
	CategoryID uint64 `json:"category_id" form:"category_id"`
	StartsWith string `json:"starts_with" form:"starts_with"`
	Country    string `json:"country" form:"country"`       // 国家代码，用于筛选
	CountryID  uint64 `json:"country_id" form:"country_id"` // 国家ID，用于筛选
}

type CreateMerchantReq struct {
	Name                string            `json:"name"`                  // 商家名称
	UniqueName          string            `json:"unique_name"`           // 商家唯一名称
	Logo                string            `json:"logo"`                  // 商家logo
	Website             string            `json:"website"`               // 商家网站
	TrackURL            string            `json:"track_url"`             // 跟踪链接
	AffiliateLink       string            `json:"affiliate_link"`        // 上级联盟链接，不带有用户编码的
	CashbackType        int8              `json:"cashback_type"`         // 固定金额、比例（默认）
	CustomParams        string            `json:"custom_params"`         // 自定义参数，格式：param1=value1&param2=value2
	ParamMappings       map[string]string `json:"param_mappings"`        // 参数映射关系，如：{"user_code": "u1", "sub1": "s1"}
	Description         string            `json:"description"`           // 商家描述
	CashbackValue       decimal.Decimal   `json:"cashback_value"`        // 基于上级联盟的返现值计算得出的具体返现金额比例。例如，用户最终能拿到的返现比例为 19.2%。优先：parent_cashback_value * cashback_rate = cashback_value
	CashbackRate        decimal.Decimal   `json:"cashback_rate"`         // 自己平台从上级联盟分配到的返现金额中，再次分配给用户的比例。例如，上级联盟的返现值为 24%，自己平台设置的返现率为 80%，则用户最终拿到的返现比例为 80% × 24% = 19.2%。
	ParentCashbackValue decimal.Decimal   `json:"parent_cashback_value"` // 上级联盟 基于订单金额计算的具体返现金额的比例值。例如，订单金额为 100 美元，2% 表示返现值为 2 美元。
	ParentCashbackRate  decimal.Decimal   `json:"parent_cashback_rate"`  // 上级联盟分配给用户的返现金额比例。比如，80% 表示上级联盟将获得的返现金额的 80% 分给用户。
	PlatformType        string            `json:"platform_type"`         // 平台类型：cj、awin
	PlatformMerchantID  string            `json:"platform_merchant_id"`  // 平台商家ID
	CategoryID          uint64            `json:"category_id"`           // 分类ID
	Featured            bool              `json:"featured"`              // 是否推荐：如果有单，则为 true
	CountryID           uint64            `json:"country_id"`            // 国家ID
	SupportedCountries  []string          `json:"supported_countries"`   // 支持返利的国家（仅仅用于展示，实际没关系）
}

type UpdateMerchantReq struct {
	ID                 uint64            `json:"id"`
	Name               string            `json:"name"`                 // 商家名称
	UniqueName         string            `json:"unique_name"`          // 商家唯一名称
	MerchantCode       string            `json:"merchant_code"`        // 商家编码
	Logo               string            `json:"logo"`                 // 商家logo
	Website            string            `json:"website"`              // 商家网站
	TrackURL           string            `json:"track_url"`            // 跟踪链接
	AffiliateLink      string            `json:"affiliate_link"`       // 上级联盟链接，不带有用户编码的
	CommissionType     int8              `json:"commission_type"`      // 佣金类型：固定金额 / 比例
	CommissionValue    decimal.Decimal   `json:"commission_value"`     // 佣金值： 直接展示的佣金比例，例如：2%
	CustomParams       string            `json:"custom_params"`        // 自定义参数，格式：param1=value1&param2=value2
	ParamMappings      map[string]string `json:"param_mappings"`       // 参数映射关系，如：{"user_code": "u1", "sub1": "s1"}
	Description        string            `json:"description"`          // 商家描述
	Status             int8              `json:"status"`               // 状态
	CashbackRate       decimal.Decimal   `json:"cashback_rate"`        // 默认返现比例: CashbackRate 上级联盟返现比例(0.3) * CashbackRate(0.8) = CommissionValue(0.24)
	PlatformType       string            `json:"platform_type"`        // 平台类型：cj、awin
	PlatformMerchantID string            `json:"platform_merchant_id"` // 平台商家ID
	CategoryID         uint64            `json:"category_id"`          // 分类ID
	Featured           bool              `json:"featured"`             // 是否推荐：如果有单，则为 true
	CountryID          uint64            `json:"country_id"`           // 国家ID
	SupportedCountries []string          `json:"supported_countries"`  // 支持返利的国家（仅仅用于展示，实际没关系）
}

// 响应对象

type MerchantDetailResp struct {
	ID                 uint64              `json:"id"`
	Name               string              `json:"name"`                // 商家名称
	UniqueName         string              `json:"unique_name"`         // 商家唯一名称
	MerchantCode       string              `json:"merchant_code"`       // 商家编码
	Logo               string              `json:"logo"`                // 商家logo
	Website            string              `json:"website"`             // 商家网站
	TrackURL           string              `json:"track_url"`           // 跟踪链接
	CashbackValue      string              `json:"cashback_value"`      // 基于上级联盟的返现值计算得出的具体返现金额比例。例如，用户最终能拿到的返现比例为 19.2%。优先：parent_cashback_value * cashback_rate = cashback_value
	Description        string              `json:"description"`         // 商家描述
	Category           *CategoryDetailResp `json:"category"`            // 分类
	Featured           bool                `json:"featured"`            // 是否推荐：如果有单，则为 true
	Country            *CountryResp        `json:"country,omitempty"`   // 国家信息
	SupportedCountries []string            `json:"supported_countries"` // 支持返利的国家（仅仅用于展示，实际没关系）
	CreatedAt          time.Time           `json:"created_at"`
	UpdatedAt          time.Time           `json:"updated_at"`
}

type MerchantListResp struct {
	Total        int64                 `json:"total"`
	Page         int                   `json:"page"`
	PageSize     int                   `json:"page_size"`
	MerchantList []*MerchantDetailResp `json:"merchant_list"`
}

// Dto2ConditionGetMerchantList 获取商家列表请求转换为 condition
func (req *GetMerchantListReq) Dto2ConditionGetMerchantList() (condition map[string]interface{}) {
	condition = map[string]interface{}{}
	// 分页处理
	offset := (req.Page - 1) * req.PageSize
	condition["offset"] = offset
	if req.Page > 0 {
		condition["page"] = req.Page
	}
	if req.PageSize > 0 {
		condition["page_size"] = req.PageSize
	}
	if req.Search != "" {
		condition["search"] = req.Search
	}
	if req.Sort != "" {
		condition["sort"] = req.Sort
	}
	if req.Featured {
		condition["featured"] = req.Featured
	}
	if req.CategoryID > 0 {
		condition["category_id"] = req.CategoryID
	}
	if req.StartsWith != "" {
		condition["starts_with"] = req.StartsWith
	}
	if req.Country != "" {
		condition["country"] = req.Country
	}
	if req.CountryID > 0 {
		condition["country_id"] = req.CountryID
	}
	return condition
}

func (req *CreateMerchantReq) Dto2EntityCreateMerchantReq() (merchant *entity.Merchant) {
	merchant = &entity.Merchant{}
	merchant.Name = req.Name
	merchant.UniqueName = req.UniqueName
	// 创建时，随机固定一个商家编码
	merchant.MerchantCode = "123"
	merchant.Logo = req.Logo
	merchant.Website = req.Website
	merchant.TrackURL = req.TrackURL
	merchant.AffiliateLink = req.AffiliateLink
	//merchant.CashbackType = req.CashbackType
	//merchant.ParentCashbackValue = req.ParentCashbackValue
	//merchant.ParentCashbackRate = req.ParentCashbackRate
	//merchant.CashbackRate = req.CashbackRate
	//
	//if req.ParentCashbackValue.GreaterThan(decimal.NewFromInt(0)) {
	//	merchant.CashbackValue = merchant.ParentCashbackValue.Mul(merchant.CashbackRate)
	//} else {
	//	merchant.CashbackValue = req.CashbackValue
	//}

	merchant.ParamMappings = req.ParamMappings
	merchant.Description = req.Description
	merchant.Status = constant.MerchantStatusEnabled
	merchant.CashbackRate = req.CashbackRate
	merchant.PlatformType = req.PlatformType
	merchant.PlatformMerchantID = req.PlatformMerchantID
	merchant.CategoryID = req.CategoryID
	merchant.Featured = req.Featured
	merchant.CountryID = req.CountryID
	merchant.SupportedCountries = req.SupportedCountries
	merchant.CreatedAt = time.Now()
	merchant.UpdatedAt = time.Now()

	return
}

func Entity2DtoMerchantDetailResp(merchant *entity.Merchant, category *entity.Category) (resp *MerchantDetailResp) {
	resp = &MerchantDetailResp{}
	resp.ID = merchant.ID
	resp.Name = merchant.Name
	resp.UniqueName = merchant.UniqueName
	resp.MerchantCode = merchant.MerchantCode
	resp.Logo = merchant.Logo
	resp.Website = merchant.Website
	resp.TrackURL = merchant.TrackURL

	commissionInfo := new(cashbackutils.CommissionInfo)
	commissionInfo.Type = merchant.CashbackType
	commissionInfo.Value, _ = merchant.CashbackValue.Float64()
	commissionInfo.IsUpTo = merchant.CashbackIsUpto
	commissionInfo.IsRevShare = merchant.CashbackIsRevShare
	commissionInfo.AlternativeValue, _ = merchant.AlternativeCashbackValue.Float64()
	commissionInfo.AlternativeType = merchant.AlternativeCashbackType
	commissionInfo.AlternativeIsUpTo = merchant.AlternativeCashbackIsUpto
	commissionInfo.AlternativeIsRevShare = merchant.AlternativeCashbackIsRevShare
	commissionInfoStr := cashbackutils.FormatCommissionInfo(commissionInfo)
	resp.CashbackValue = commissionInfoStr
	resp.Description = merchant.Description
	// 转换分类信息
	if category != nil {
		resp.Category = Entity2DtoCategoryDetailResp(category)
	} else {
		resp.Category = &CategoryDetailResp{}
	}
	resp.Featured = merchant.Featured
	// 转换国家信息
	if merchant.Country != nil {
		resp.Country = &CountryResp{
			ID:       merchant.Country.ID,
			Name:     merchant.Country.Name,
			Code:     merchant.Country.Code,
			Flag:     merchant.Country.Flag,
			Currency: merchant.Country.Currency,
			Status:   merchant.Country.Status,
		}
	}
	resp.SupportedCountries = merchant.SupportedCountries
	resp.CreatedAt = merchant.CreatedAt
	resp.UpdatedAt = merchant.UpdatedAt
	return resp
}

func Entity2DtoMerchantListResp(pageSize int, page int, total int64, merchantList []*entity.Merchant, categoryMap map[uint64]*entity.Category) (resp *MerchantListResp) {
	resp = &MerchantListResp{}
	resp.Total = total
	resp.PageSize = pageSize
	resp.Page = page
	for i := range merchantList {
		if category, ok := categoryMap[merchantList[i].CategoryID]; ok {
			resp.MerchantList = append(resp.MerchantList, Entity2DtoMerchantDetailResp(merchantList[i], category))
		} else {
			category = &entity.Category{}
			resp.MerchantList = append(resp.MerchantList, Entity2DtoMerchantDetailResp(merchantList[i], category))
		}
	}
	return resp
}
