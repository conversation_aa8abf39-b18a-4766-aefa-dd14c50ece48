#!/bin/bash

# 多国家功能测试脚本

echo "🌍 测试多国家数据展示功能"
echo "================================"

# API服务器地址
API_BASE="http://localhost:8080/api/v1"

echo ""
echo "1. 测试国家列表API..."
echo "GET $API_BASE/countries"
curl -s -X GET "$API_BASE/countries" | jq '.country_list | length' | xargs echo "✅ 返回国家数量:"

echo ""
echo "2. 测试美国商家数据..."
echo "GET $API_BASE/merchants?country=US&page=1&page_size=5"
US_COUNT=$(curl -s -X GET "$API_BASE/merchants?country=US&page=1&page_size=5" | jq '.total')
echo "✅ 美国商家总数: $US_COUNT"

echo ""
echo "3. 测试英国商家数据..."
echo "GET $API_BASE/merchants?country=UK&page=1&page_size=5"
UK_COUNT=$(curl -s -X GET "$API_BASE/merchants?country=UK&page=1&page_size=5" | jq '.total')
echo "✅ 英国商家总数: $UK_COUNT"

echo ""
echo "4. 测试加拿大商家数据..."
echo "GET $API_BASE/merchants?country=CA&page=1&page_size=5"
CA_COUNT=$(curl -s -X GET "$API_BASE/merchants?country=CA&page=1&page_size=5" | jq '.total')
echo "✅ 加拿大商家总数: $CA_COUNT"

echo ""
echo "5. 验证商家数据包含国家信息..."
SAMPLE_MERCHANT=$(curl -s -X GET "$API_BASE/merchants?country=US&page=1&page_size=1" | jq '.merchant_list[0]')
HAS_COUNTRY=$(echo $SAMPLE_MERCHANT | jq 'has("country")')
echo "✅ 商家数据包含国家字段: $HAS_COUNTRY"

if [ "$HAS_COUNTRY" = "true" ]; then
    COUNTRY_NAME=$(echo $SAMPLE_MERCHANT | jq -r '.country.name // "N/A"')
    COUNTRY_CODE=$(echo $SAMPLE_MERCHANT | jq -r '.country.code // "N/A"')
    echo "   国家名称: $COUNTRY_NAME"
    echo "   国家代码: $COUNTRY_CODE"
fi

echo ""
echo "6. 测试前端应用..."
echo "检查前端是否在 http://localhost:3001 运行..."
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001)
if [ "$FRONTEND_STATUS" = "200" ]; then
    echo "✅ 前端应用正常运行"
else
    echo "❌ 前端应用无法访问 (HTTP $FRONTEND_STATUS)"
fi

echo ""
echo "================================"
echo "🎉 多国家功能测试完成！"
echo ""
echo "📋 测试结果总结:"
echo "   - 国家API: ✅ 正常"
echo "   - 美国商家: $US_COUNT 个"
echo "   - 英国商家: $UK_COUNT 个" 
echo "   - 加拿大商家: $CA_COUNT 个"
echo "   - 商家国家字段: $HAS_COUNTRY"
echo "   - 前端应用: $([ "$FRONTEND_STATUS" = "200" ] && echo "✅ 正常" || echo "❌ 异常")"
echo ""
echo "🌐 前端测试步骤:"
echo "   1. 访问 http://localhost:3001"
echo "   2. 检查导航栏是否显示国家选择器"
echo "   3. 切换不同国家，观察商家数据变化"
echo "   4. 验证选择的国家是否保存到localStorage"
echo ""
