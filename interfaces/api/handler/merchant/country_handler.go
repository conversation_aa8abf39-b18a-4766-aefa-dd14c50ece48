package merchant

import (
	"bonusearned/application/merchant/dto"
	"bonusearned/domain/merchant/service"
	"bonusearned/infra/constant"
	"bonusearned/infra/ecode"
	"bonusearned/interfaces/api/response"
	"github.com/gin-gonic/gin"
	"strconv"
)

// CountryHandler 国家处理器
type CountryHandler struct {
	countryService service.CountryService
}

// NewCountryHandler 创建国家处理器
func NewCountryHandler(countryService service.CountryService) *CountryHandler {
	return &CountryHandler{
		countryService: countryService,
	}
}

// GetCountryList godoc
// @Summary 获取国家列表
// @Description 获取国家列表
// @Tags countries
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param search query string false "搜索关键词"
// @Param status query int false "状态"
// @Success 200 {object} dto.CountryListResponse
// @Router /api/v1/countries [get]
func (h *CountryHandler) GetCountryList(c *gin.Context) {
	var req dto.GetCountryListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, ecode.ErrInvalidParameter.Message)
		return
	}

	// 设置默认值和参数验证
	if req.Page <= 0 {
		req.Page = constant.DefaultPage
	}
	if req.PageSize <= 0 {
		req.PageSize = constant.DefaultPageSize
	}
	if req.PageSize > constant.MaxPageSize {
		req.PageSize = constant.MaxPageSize
	}

	// 构建查询条件
	condition := make(map[string]interface{})
	if req.Search != "" {
		condition["name"] = req.Search
	}
	if req.Status != nil {
		condition["status"] = *req.Status
	}

	// 获取国家列表
	countries, total, err := h.countryService.GetCountryListByCondition(c, condition)
	if err != nil {
		response.Error(c, err.Code, err.Message)
		return
	}

	// 构建响应
	resp := dto.CountryListResponse{
		Total:       total,
		Page:        req.Page,
		PageSize:    req.PageSize,
		CountryList: dto.ToCountryRespList(countries),
	}

	response.Success(c, resp)
}

// GetCountryDetail godoc
// @Summary 获取国家详情
// @Description 根据ID获取国家详情
// @Tags countries
// @Accept json
// @Produce json
// @Param id path int true "国家ID"
// @Success 200 {object} dto.CountryResp
// @Router /api/v1/countries/{id} [get]
func (h *CountryHandler) GetCountryDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, ecode.ErrInvalidID.Message)
		return
	}

	country, serviceErr := h.countryService.GetCountryDetailById(c, id)
	if serviceErr != nil {
		response.Error(c, serviceErr.Code, serviceErr.Message)
		return
	}

	resp := dto.ToCountryResp(country)
	response.Success(c, resp)
}

// CreateCountry godoc
// @Summary 创建国家
// @Description 创建新的国家
// @Tags countries
// @Accept json
// @Produce json
// @Param country body dto.CreateCountryReq true "国家信息"
// @Success 200 {object} dto.CountryResp
// @Router /api/v1/countries [post]
func (h *CountryHandler) CreateCountry(c *gin.Context) {
	var req dto.CreateCountryReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	country := req.ToCountryEntity()
	if err := h.countryService.CreateCountry(c, country); err != nil {
		response.Error(c, err.Code, err.Message)
		return
	}

	resp := dto.ToCountryResp(country)
	response.Success(c, resp)
}

// UpdateCountry godoc
// @Summary 更新国家
// @Description 更新国家信息
// @Tags countries
// @Accept json
// @Produce json
// @Param id path int true "国家ID"
// @Param country body dto.UpdateCountryReq true "国家信息"
// @Success 200 {object} dto.CountryResp
// @Router /api/v1/countries/{id} [put]
func (h *CountryHandler) UpdateCountry(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, ecode.ErrInvalidID.Message)
		return
	}

	var req dto.UpdateCountryReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, ecode.ErrInvalidParameter.Code, err.Error())
		return
	}

	req.ID = id
	country := req.ToCountryEntity()
	if err := h.countryService.UpdateCountry(c, country); err != nil {
		response.Error(c, err.Code, err.Message)
		return
	}

	// 获取更新后的国家信息
	updatedCountry, serviceErr := h.countryService.GetCountryDetailById(c, id)
	if serviceErr != nil {
		response.Error(c, serviceErr.Code, serviceErr.Message)
		return
	}

	resp := dto.ToCountryResp(updatedCountry)
	response.Success(c, resp)
}

// DeleteCountry godoc
// @Summary 删除国家
// @Description 删除国家
// @Tags countries
// @Accept json
// @Produce json
// @Param id path int true "国家ID"
// @Success 200 {string} string "success"
// @Router /api/v1/countries/{id} [delete]
func (h *CountryHandler) DeleteCountry(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(c, ecode.ErrInvalidID.Code, ecode.ErrInvalidID.Message)
		return
	}

	if err := h.countryService.DeleteCountry(c, id); err != nil {
		response.Error(c, err.Code, err.Message)
		return
	}

	response.Success(c, "success")
}
