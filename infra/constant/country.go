package constant

// CountryCodeToName 国家代码到国家名称的映射
var CountryCodeToName = map[string]string{
	"US": "United States",
	"UK": "United Kingdom",
	"CA": "Canada",
	"AU": "Australia",
	"DE": "Germany",
	"FR": "France",
	"IT": "Italy",
	"ES": "Spain",
	"NL": "Netherlands",
	"BE": "Belgium",
	"CH": "Switzerland",
	"AT": "Austria",
	"SE": "Sweden",
	"NO": "Norway",
	"DK": "Denmark",
	"FI": "Finland",
	"PL": "Poland",
	"CZ": "Czech Republic",
	"HU": "Hungary",
	"PT": "Portugal",
	"IE": "Ireland",
	"GR": "Greece",
	"JP": "Japan",
	"KR": "South Korea",
	"SG": "Singapore",
	"HK": "Hong Kong",
	"TW": "Taiwan",
	"NZ": "New Zealand",
	"BR": "Brazil",
	"MX": "Mexico",
	"IN": "India",
	"CN": "China",
	"RU": "Russia",
	"ZA": "South Africa",
	"IL": "Israel",
	"TR": "Turkey",
	"AE": "United Arab Emirates",
	"SA": "Saudi Arabia",
	"TH": "Thailand",
	"MY": "Malaysia",
	"ID": "Indonesia",
	"PH": "Philippines",
	"VN": "Vietnam",
	"AR": "Argentina",
	"CL": "Chile",
	"CO": "Colombia",
	"PE": "Peru",
	"EG": "Egypt",
	"NG": "Nigeria",
	"KE": "Kenya",
}

// CountryNameToCode 国家名称到国家代码的映射
var CountryNameToCode = map[string]string{
	"United States":         "US",
	"United Kingdom":        "UK",
	"Canada":                "CA",
	"Australia":             "AU",
	"Germany":               "DE",
	"France":                "FR",
	"Italy":                 "IT",
	"Spain":                 "ES",
	"Netherlands":           "NL",
	"Belgium":               "BE",
	"Switzerland":           "CH",
	"Austria":               "AT",
	"Sweden":                "SE",
	"Norway":                "NO",
	"Denmark":               "DK",
	"Finland":               "FI",
	"Poland":                "PL",
	"Czech Republic":        "CZ",
	"Hungary":               "HU",
	"Portugal":              "PT",
	"Ireland":               "IE",
	"Greece":                "GR",
	"Japan":                 "JP",
	"South Korea":           "KR",
	"Singapore":             "SG",
	"Hong Kong":             "HK",
	"Taiwan":                "TW",
	"New Zealand":           "NZ",
	"Brazil":                "BR",
	"Mexico":                "MX",
	"India":                 "IN",
	"China":                 "CN",
	"Russia":                "RU",
	"South Africa":          "ZA",
	"Israel":                "IL",
	"Turkey":                "TR",
	"United Arab Emirates":  "AE",
	"Saudi Arabia":          "SA",
	"Thailand":              "TH",
	"Malaysia":              "MY",
	"Indonesia":             "ID",
	"Philippines":           "PH",
	"Vietnam":               "VN",
	"Argentina":             "AR",
	"Chile":                 "CL",
	"Colombia":              "CO",
	"Peru":                  "PE",
	"Egypt":                 "EG",
	"Nigeria":               "NG",
	"Kenya":                 "KE",
}

// GetCountryCodeByName 根据国家名称获取国家代码
func GetCountryCodeByName(name string) (string, bool) {
	code, exists := CountryNameToCode[name]
	return code, exists
}

// GetCountryNameByCode 根据国家代码获取国家名称
func GetCountryNameByCode(code string) (string, bool) {
	name, exists := CountryCodeToName[code]
	return name, exists
}

// IsValidCountryCode 检查是否为有效的国家代码
func IsValidCountryCode(code string) bool {
	_, exists := CountryCodeToName[code]
	return exists
}

// IsValidCountryName 检查是否为有效的国家名称
func IsValidCountryName(name string) bool {
	_, exists := CountryNameToCode[name]
	return exists
}
