package entity

import (
	"gorm.io/gorm"
	"time"
)

// Country 国家实体
type Country struct {
	ID        uint64         `gorm:"primarykey" json:"id"`
	Name      string         `gorm:"type:varchar(100);not null" json:"name"`           // 国家名称
	Code      string         `gorm:"type:varchar(10);uniqueIndex;not null" json:"code"` // 国家代码 (如: US, UK, CA)
	Flag      string         `gorm:"type:varchar(255)" json:"flag"`                    // 国旗图标URL
	Currency  string         `gorm:"type:varchar(10)" json:"currency"`                 // 货币代码 (如: USD, GBP, CAD)
	Status    int8           `gorm:"type:smallint;default:1;not null" json:"status"`   // 状态：1-启用，0-禁用
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Country) TableName() string {
	return "countries"
}
