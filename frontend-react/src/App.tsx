import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { StoreProvider } from './contexts/StoreContext';
import { CouponProvider } from './contexts/CouponContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import MainLayout from './components/layout/MainLayout';
import Home from './pages/Home';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import Profile from './pages/profile';
import Stores from './pages/stores';
import StoreDetail from './pages/stores/detail';
import Orders from './pages/orders';
import ClickRecords from './pages/ClickRecords';
import NotFound from './pages/NotFound';
import Blog from './pages/Blog';
import BlogDetail from './pages/BlogDetail';
import Coupons from './pages/coupons/Coupons';
import AboutUs from './pages/AboutUs';
import Contact from './pages/Contact';
import HelpCenter from './pages/HelpCenter';
import TermsOfService from './pages/TermsOfService';
import PrivacyPolicy from './pages/PrivacyPolicy';
import HeadInjector from './components/HeadInjector';
import BodyInjector from './components/BodyInjector';

// 滚动到顶部的包装组件
function ScrollToTop() {
  const { pathname } = useLocation();

  React.useEffect(() => {
    // 在登录和注册页面不执行滚动，以避免与动画效果冲突
    if (pathname.includes('/auth/login') || pathname.includes('/auth/register')) {
      return;
    }

    // 使用 requestAnimationFrame 确保在 DOM 更新后再滚动
    requestAnimationFrame(() => {
      window.scrollTo({
        top: 0,
        behavior: 'instant'
      });
    });
  }, [pathname]);

  return null;
}

function App() {
  return (
    <AuthProvider>
      <StoreProvider>
        <CouponProvider>
          <HeadInjector />
          <BodyInjector />
          <MainLayout>
            <ScrollToTop />
            <Outlet />
            <ToastContainer
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
          </MainLayout>
        </CouponProvider>
      </StoreProvider>
    </AuthProvider>
  );
}

export default App;
