export interface Category {
  id: number
  name: string
  icon?: string
  description?: string
  image?: string
  parent_id?: number
}

export interface Store {
  id: number
  name: string
  unique_name: string
  merchant_code?: string
  logo?: string
  image?: string
  website?: string
  track_url?: string
  commission_type: number
  commission_value: number
  cashback_type?: number
  cashback_value?: string
  description?: string
  categories?: Category[]
  category?: Category
  featured?: boolean
  country?: string
  supported_countries?: string[] | null
  created_at?: string
  updated_at?: string
}

export interface StoreResponse {
  total: number
  page: number
  page_size: number
  merchant_list: Store[]
}

export interface User {
  id: string
  email: string
  nickname: string
  phone?: string
  status: number
  user_code: string
  payment_info?: {
    paypal_email?: string
    bank_account?: string
    bank_name?: string
    bank_branch?: string
  }
  last_login_at?: string
  last_login_ip?: string
}

export interface Profile {
  user: User
  balance: number
  transactions: Transaction[]
}

export interface Transaction {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  createdAt: string
  store: Store
}

export interface Coupon {
  id: number
  merchant_info?: Store
  code: string
  discount_rate: string
  title: string
  description?: string
  type: string
  started_at?: string
  ended_at?: string
}
