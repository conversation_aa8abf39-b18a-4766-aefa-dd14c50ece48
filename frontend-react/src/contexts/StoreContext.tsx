import React, { createContext, useContext, useState, useCallback, useRef } from 'react'
import { Store, StoreResponse, Category } from '../types'
import api from '../lib/api'
import { useCountry } from './CountryContext'

interface GetStoresParams {
  page?: number
  page_size?: number
  sort?: string
  category?: string
  category_id?: number
  keyword?: string
  starts_with?: string
  featured?: boolean
  country?: string
  country_id?: number
}

interface StoreContextType {
  getStore: (uniqueName: string) => Promise<Store>
  getStores: (params: GetStoresParams) => Promise<StoreResponse>
  getSimilarStores: (params: {
    category_id: number
    exclude: number
    limit: number
  }) => Promise<StoreResponse>
  getCategories: () => Promise<Category[]>
  clearCategoryCache: () => void
}

interface CacheItem<T> {
  data: T
  timestamp: number
}

const CATEGORY_CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours

const StoreContext = createContext<StoreContextType | undefined>(undefined)

export function StoreProvider({ children }: { children: React.ReactNode }) {
  const { selectedCountry } = useCountry()
  // 只缓存分类数据
  const categoryCache = useRef<CacheItem<Category[]> | null>(null)

  // 清除分类缓存的方法
  const clearCategoryCache = useCallback(() => {
    categoryCache.current = null
  }, [])

  // 检查分类缓存是否过期
  const isCategoryCacheValid = useCallback(() => {
    const cache = categoryCache.current
    if (!cache) return false
    return Date.now() - cache.timestamp < CATEGORY_CACHE_DURATION
  }, [])

  const getStore = useCallback(async (uniqueName: string): Promise<Store> => {
    try {
      const response = await api.get<Store>(`/merchants/${uniqueName}`)
      return response
    } catch (error) {
      console.error('Error fetching store:', error)
      throw error
    }
  }, [])

  // 获取商家列表
  const getStores = useCallback(async (params: GetStoresParams): Promise<StoreResponse> => {
    try {
      // 自动添加当前选中的国家参数
      const finalParams = {
        ...params,
        country: selectedCountry?.code || 'US'
      }
      const response = await api.get<StoreResponse>('/merchants', { params: finalParams })

      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format')
      }

      // 确保所有必要的字段都存在且类型正确
      const total = typeof response.total === 'number' ? response.total : 0
      const page = typeof response.page === 'number' ? response.page : (params.page || 1)
      const pageSize = typeof response.page_size === 'number' ? response.page_size : (params.page_size || 30)
      const merchantList = Array.isArray(response.merchant_list) ? response.merchant_list : []

      return {
        total,
        page,
        page_size: pageSize,
        merchant_list: merchantList
      }
    } catch (error) {
      console.error('Error fetching stores:', error)
      throw error
    }
  }, [selectedCountry])

  const getSimilarStores = useCallback(async (params: {
    category_id: number
    exclude: number
    limit: number
  }): Promise<StoreResponse> => {
    return api.get<StoreResponse>('/merchants', { params })
  }, [])

  const getCategories = useCallback(async (): Promise<Category[]> => {
    try {
      // Check cache first
      if (isCategoryCacheValid() && categoryCache.current) {
        return categoryCache.current.data
      }

      const response = await api.get<{
        total: number;
        category_list: Category[];
        page: number;
        page_size: number;
      }>('/categories', {
        params: {
          page: 1,
          page_size: 1000 // 增加数量以确保获取所有分类
        }
      })

      // Update cache
      categoryCache.current = {
        data: response.category_list,
        timestamp: Date.now()
      }

      return response.category_list
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error
    }
  }, [isCategoryCacheValid])

  const value = {
    getStore,
    getStores,
    getSimilarStores,
    getCategories,
    clearCategoryCache
  }

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  )
}

export function useStore() {
  const context = useContext(StoreContext)
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider')
  }
  return context
}
