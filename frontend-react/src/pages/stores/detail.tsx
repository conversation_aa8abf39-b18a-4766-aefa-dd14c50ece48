import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { useStore } from '../../contexts/StoreContext'
import { Store, Coupon } from '../../types'
import { motion, AnimatePresence } from 'framer-motion'
import { FaGlobe, FaExternalLinkAlt, FaShare, FaCopy, FaInfoCircle, FaRegClock, FaArrowLeft } from 'react-icons/fa'
import api from '../../lib/api'
import './detail.css'
// Helmet用于设置页面元数据
import Helmet from 'react-helmet'

interface SimilarStore {
  id: number
  name: string
  logo?: string
  image?: string
  cashback_value: string
  unique_name: string
}

const StoreDetail = () => {
  const { unique_name } = useParams()
  const navigate = useNavigate()
  const { getStore, getSimilarStores } = useStore()
  const [store, setStore] = useState<Store | null>(null)
  const [similarStores, setSimilarStores] = useState<SimilarStore[]>([])
  const [coupons, setCoupons] = useState<Coupon[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null)
  const [isCopied, setIsCopied] = useState(false)
  const [countdown, setCountdown] = useState<number | null>(null)
  const [notification, setNotification] = useState({ message: '', type: '' })
  const [showShareModal, setShowShareModal] = useState(false)
  const [showTrackUrlModal, setShowTrackUrlModal] = useState(false)
  const [sub1, setSub1] = useState('')
  const [sub1Error, setSub1Error] = useState('')
  const [isMobile, setIsMobile] = useState(false)
  const shareUrl = store?.track_url ? `${store.track_url}${sub1 ? `${store.track_url.includes('?') ? '&' : '?'}sub1=${sub1}` : ''}` : ''
  
  // 检测移动端设备
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
    }
    
    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  const generateShareText = () => {
    if (!store) return ''
    const text = `🛍️ ${store.name} Cashback Offer!\n💰 Up to ${store.cashback_value} Cashback\n🔥 Shop Now:`
    return encodeURIComponent(text)
  }

  const handleSub1Change = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    // Only allow letters, numbers and underscore, max 5 characters
    if (value === '' || /^[a-zA-Z0-9_]{1,5}$/.test(value)) {
      setSub1(value)
      setSub1Error('')
    } else {
      setSub1Error('Only letters, numbers and underscore allowed (max 5 characters)')
    }
  }
  const handleShare = async () => {
    try {
      if (navigator.share && isMobile) {
        // 移动端使用Web Share API
        await navigator.share({
          title: `${store?.name} Cashback & Coupons`,
          text: decodeURIComponent(generateShareText()),
          url: shareUrl
        });
        setNotification({
          message: 'Thanks for sharing!',
          type: 'success'
        });
      } else {
        // 桌面端复制链接
        await navigator.clipboard.writeText(shareUrl);
        setNotification({
          message: 'Link copied to clipboard!',
          type: 'success'
        });
        
        // 设置按钮文字为 Copied
        const copyButton = document.querySelector('.share-info .copy-button');
        if (copyButton) {
          copyButton.textContent = 'Copied';
          
          // 3秒后恢复按钮文字
          setTimeout(() => {
            copyButton.textContent = 'Copy Link';
          }, 3000);
        }
      }
    } catch (error) {
      console.error('Failed to share/copy:', error);
      setNotification({
        message: 'Failed to share/copy link',
        type: 'error'
      });
    }
  };

  useEffect(() => {
    fetchStoreData()
  }, [unique_name])

  const fetchStoreData = async () => {
    if (!unique_name) {
      navigate('/stores')
      return
    }

    try {
      setLoading(true)
      const storeData = await getStore(unique_name)
      setStore(storeData)

      // Fetch similar stores
      const similarResponse = await getSimilarStores({
        category_id: storeData.categories?.[0]?.id || 0, // 提供默认值以修复类型错误
        exclude: storeData.id,
        limit: isMobile ? 4 : 6 // 移动端减少加载数量
      })
      
      // 修复类型错误
      const typedSimilarStores = similarResponse.merchant_list?.map((store: any) => ({
        id: store.id,
        name: store.name,
        logo: store.logo,
        image: store.image,
        cashback_value: store.cashback_value || '',
        unique_name: store.unique_name
      })) || [];
      
      setSimilarStores(typedSimilarStores)

      // Fetch coupons
      if (storeData.id) {
        const couponsResponse: any = await api.get('/coupons', {
          params: {
            merchant_id: storeData.id,
            page: 1,
            page_size: isMobile ? 4 : 10 // 移动端减少初始加载数量
          }
        })
        setCoupons(couponsResponse.coupon_list || [])
      }
    } catch (error) {
      setError('Failed to load store details')
      console.error('Error loading store:', error)
    } finally {
      setLoading(false)
    }
  }

  // 安全访问store的方法
  const safeTrackUrl = () => store?.track_url || '';

  const handleCouponClick = (coupon: Coupon) => {
    // 检查是否是通过URL参数访问
    const urlParams = new URLSearchParams(window.location.search);
    const urlCouponId = urlParams.get('couponId');
    const urlCouponCode = urlParams.get('code');
    const isUrlAccess = urlCouponId === coupon.id.toString() && urlCouponCode === coupon.code;

    if (!isUrlAccess) {
      // 在新标签页打开当前页面并带上优惠券参数
      const newTabUrl = new URL(window.location.href);
      newTabUrl.searchParams.set('couponId', coupon.id.toString());
      newTabUrl.searchParams.set('code', coupon.code);
      window.open(newTabUrl.toString(), '_blank');

      // 当前页面跳转到商家链接
      if (store?.track_url) {
        window.location.href = store.track_url;
      }
      return;
    }

    setSelectedCoupon(coupon);
    setIsCopied(false);
    setCountdown(0);
  };

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setIsCopied(true);
      
      // 显示通知
      setNotification({
        message: 'Code copied! Will open store in 3 seconds',
        type: 'success'
      });
      
      // 开始倒计时
      setCountdown(3);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            // 在新标签页打开商家链接
            if (store?.track_url) {
              window.open(store.track_url, '_blank');
            }
            setCountdown(null);
            setIsCopied(false);
            return null;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error('Failed to copy:', error);
      setNotification({
        message: 'Failed to copy code',
        type: 'error'
      });
    }
  };

  // 检查URL中是否有优惠券参数
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const couponId = urlParams.get('couponId');
    const code = urlParams.get('code');
    
    if (couponId && code && coupons.length > 0) {
      const coupon = coupons.find(c => c.id.toString() === couponId && c.code === code);
      if (coupon) {
        setSelectedCoupon(coupon);
      }
    }
  }, [coupons]);

  // 将优惠券代码显示为部分隐藏的形式
  const maskCouponCode = (code: string) => {
    if (!code) return '';
    const length = code.length;
    const visibleLength = Math.floor(length / 3);
    return code.substring(0, visibleLength) + '****';
  };

  // SEO相关函数
  const generateSeoTitle = () => {
    if (!store) return 'Cashback Offers and Coupons | BonusEarned';
    
    return `${store.name}: Coupons & Promo Codes | BonusEarned`;
  };
  
  const generateSeoDescription = () => {
    if (!store) return 'Find the best cashback offers and discount coupons for your favorite stores.';
    
    return `Get verified ${store.name} coupons${store.cashback_value ? `, up to ${store.cashback_value} cashback` : ''} and promo codes. Save money on your next purchase with exclusive deals from BonusEarned.`;
  };
  
  const generateCanonicalUrl = () => {
    return `${window.location.origin}/stores/${unique_name}`;
  };

  // SEO相关函数
  const generateFaqSchema = () => {
    if (!store) return null;
    
    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": `How do I earn cashback at ${store.name}?`,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": `To earn cashback at ${store.name}, simply click the 'Shop Now' or 'Activate Cashback' button, shop as usual, and your cashback will be tracked automatically. Your earnings will appear in your BonusEarned account once confirmed.`
          }
        },
        {
          "@type": "Question",
          "name": `How much cashback can I earn at ${store.name}?`,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": `You can earn up to ${store.cashback_value || '5%'} cashback on your purchases at ${store.name} through BonusEarned.`
          }
        },
        {
          "@type": "Question",
          "name": `Are ${store.name} coupons free to use?`,
          "acceptedAnswer": {
            "@type": "Answer",
            "text": `Yes, all ${store.name} coupons and promo codes on BonusEarned are completely free to use. Simply copy the code and apply it at checkout to save money on your purchases.`
          }
        }
      ]
    };
  };
  
  const generateOrganizationSchema = () => {
    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "BonusEarned",
      "url": window.location.origin,
      "logo": `${window.location.origin}/images/logo.png`,
      "sameAs": [
        "https://www.facebook.com/bonusearned",
        "https://twitter.com/bonusearned",
        "https://www.instagram.com/bonusearned"
      ]
    };
  };
  
  const generateBreadcrumbSchema = () => {
    return {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": window.location.origin
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Stores",
          "item": `${window.location.origin}/stores`
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": store?.name || "Store",
          "item": generateCanonicalUrl()
        }
      ]
    };
  };

  useEffect(() => {
    if (store) {
      document.title = generateSeoTitle();
    }
  }, [store]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-600"></div>
        </div>
      </div>
    )
  }

  if (error || !store) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6 text-center">
            <p className="text-purple-600 mb-4">{error || 'Store not found'}</p>
            <button
              onClick={() => navigate('/stores')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 hover:scale-105 active:scale-95"
            >
              Back to Stores
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-12">
      {store && (
        <Helmet>
          <html lang="en" />
          <title>{generateSeoTitle()}</title>
          <meta name="description" content={generateSeoDescription()} />
          <meta name="keywords" content={`${store.name}, cashback, coupons, discount, promo codes, deals, ${store.categories?.map(cat => cat.name).join(', ')}`} />
          <link rel="canonical" href={generateCanonicalUrl()} />
          
          {/* 增强SEO的元标签 */}
          <meta name="robots" content="index, follow" />
          <meta name="author" content="BonusEarned" />
          <meta name="application-name" content="BonusEarned" />
          
          {/* Open Graph 元标签 */}
          <meta property="og:title" content={generateSeoTitle()} />
          <meta property="og:description" content={generateSeoDescription()} />
          <meta property="og:type" content="website" />
          <meta property="og:url" content={generateCanonicalUrl()} />
          <meta property="og:image" content={store.image || store.logo || `${window.location.origin}/images/default-store.jpg`} />
          <meta property="og:site_name" content="BonusEarned" />
          <meta property="og:locale" content="en_US" />
          
          {/* Twitter 卡片 */}
          <meta name="twitter:card" content="summary_large_image" />
          <meta name="twitter:title" content={generateSeoTitle()} />
          <meta name="twitter:description" content={generateSeoDescription()} />
          <meta name="twitter:image" content={store.image || store.logo || `${window.location.origin}/images/default-store.jpg`} />
          <meta name="twitter:site" content="@BonusEarned" />
          
          {/* 额外的移动端优化元标签 */}
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
          <meta name="theme-color" content="#9333ea" />
          <meta name="apple-mobile-web-app-capable" content="yes" />
          <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
          
          {/* 结构化数据 - JSON-LD - 商品信息 */}
          <script type="application/ld+json">
            {JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Product",
              "name": `${store.name} Cashback Offer`,
              "description": generateSeoDescription(),
              "offers": {
                "@type": "Offer",
                "priceCurrency": "USD",
                "price": "0",
                "seller": {
                  "@type": "Organization",
                  "name": "BonusEarned"
                },
                "availability": "https://schema.org/InStock"
              },
              "brand": {
                "@type": "Brand",
                "name": store.name
              },
              "image": store.image || store.logo || `${window.location.origin}/images/default-store.jpg`
            })}
          </script>
          
          {/* 结构化数据 - JSON-LD - FAQ页面 */}
          <script type="application/ld+json">
            {JSON.stringify(generateFaqSchema())}
          </script>
          
          {/* 结构化数据 - JSON-LD - 组织信息 */}
          <script type="application/ld+json">
            {JSON.stringify(generateOrganizationSchema())}
          </script>
          
          {/* 结构化数据 - JSON-LD - 面包屑导航 */}
          <script type="application/ld+json">
            {JSON.stringify(generateBreadcrumbSchema())}
          </script>
        </Helmet>
      )}

      {/* 移动端顶部导航 - 仅在移动端显示 */}
      {isMobile && (
        <div className="sticky top-0 z-40 bg-white shadow-sm p-3 flex items-center justify-between">
          <button
            onClick={() => navigate('/stores')}
            className="p-2 rounded-md text-purple-600 hover:text-purple-800"
          >
            <FaArrowLeft />
          </button>
          <h1 className="text-lg font-semibold text-gray-800 truncate flex-1 text-center">
            {store.name}
          </h1>
          <button
            onClick={handleShare}
            className="p-2 rounded-md text-purple-600 hover:text-purple-800"
          >
            <FaShare />
          </button>
        </div>
      )}

      {/* Notification Toast - 移动端调整位置和尺寸 */}
      <AnimatePresence>
        {notification.message && (
          <motion.div 
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className={`fixed ${isMobile ? 'bottom-2 left-2 right-2' : 'bottom-4 right-4'} z-50 px-4 md:px-6 py-2 md:py-3 rounded-lg shadow-lg ${
              notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'
            } text-white font-medium flex items-center text-sm md:text-base`}
          >
            {notification.type === 'success' ? (
              <svg className="w-4 h-4 md:w-5 md:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-4 h-4 md:w-5 md:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
            {notification.message}
          </motion.div>
        )}
      </AnimatePresence>

      <div className="container mx-auto px-3 md:px-4 py-4 md:py-10 max-w-7xl">
        {/* 面包屑导航 - 仅在非移动端显示 */}
        {!isMobile && (
          <nav className="flex mb-6 text-sm">
            <button
              onClick={() => navigate('/')}
              className="text-purple-600 hover:text-purple-800 transition-colors duration-200"
            >
              Home
            </button>
            <span className="mx-2 text-gray-400">/</span>
            <button 
              onClick={() => navigate('/stores')}
              className="text-purple-600 hover:text-purple-800 transition-colors duration-200"
            >
              Stores
            </button>
            <span className="mx-2 text-gray-400">/</span>
            <span className="text-gray-600">{store.name}</span>
          </nav>
        )}

        {/* Store Header - 适配移动端 */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-2xl shadow-md mb-6 md:mb-8 overflow-hidden"
        >
          {/* Decorative Top Pattern */}
          <div className="h-2 md:h-3 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-400"></div>
          
          <div className={`p-4 ${isMobile ? '' : 'md:p-8'} relative`}>
            <div className="absolute top-0 right-0 w-64 h-64 bg-purple-50 rounded-full -mt-32 -mr-32 opacity-30"></div>
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-blue-50 rounded-full -mb-20 -ml-20 opacity-30"></div>
            
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4 md:gap-8 relative z-10">
              <motion.div 
                whileHover={{ scale: isMobile ? 1.02 : 1.05, rotate: isMobile ? 0 : 2 }}
                whileTap={{ scale: 0.95 }}
                className="w-20 h-20 md:w-32 md:h-32 mx-auto md:mx-0 flex-shrink-0 bg-white rounded-2xl shadow-lg overflow-hidden p-3 border border-gray-100"
              >
                <img
                  src={store.image || store.logo || '/images/default-logo.png'}
                  alt={store.name}
                  className="w-full h-full object-contain"
                  onError={(e) => {
                    e.currentTarget.src = '/images/default-logo.png';
                  }}
                  loading="lazy"
                />
              </motion.div>
              
              <div className="flex-1 min-w-0 text-center md:text-left">
                {isMobile ? (
                  <>
                    <h1 className="text-xl font-bold text-gray-800 mb-2">{store.name}</h1>
                    <div className="flex flex-wrap justify-center md:justify-start gap-2 mb-3">
                      {store.categories?.slice(0, 2).map((category) => (
                        <motion.span
                          key={category.id}
                          whileHover={{ scale: 1.05 }}
                          className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors cursor-pointer"
                          onClick={() => navigate(`/stores?category=${category.id}`)}
                        >
                          {category.name}
                        </motion.span>
                      ))}
                      {store.country && (
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-1 text-xs font-medium text-blue-800 whitespace-nowrap">
                          <FaGlobe className="mr-1" />
                          {store.country}
                        </span>
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex flex-wrap items-center gap-3 mb-3">
                      <h1 className="text-2xl md:text-3xl font-bold truncate">Verified</h1>
                      <h1 className="text-2xl md:text-3xl font-bold text-purple-600 truncate">
                        {store.name}
                      </h1><h1 className="text-2xl md:text-3xl font-bold truncate">Coupons, Promo Codes & Cashback</h1>
                      {store.country && (
                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-1 text-xs font-medium text-blue-800 whitespace-nowrap">
                          <FaGlobe className="mr-1" />
                          {store.country}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {store.categories?.map((category) => (
                        <motion.span
                          key={category.id}
                          whileHover={{ scale: 1.05 }}
                          className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors cursor-pointer"
                          onClick={() => navigate(`/stores?category=${category.id}`)}
                        >
                          {category.name}
                        </motion.span>
                      ))}
                    </div>
                  </>
                )}

                {store.cashback_value && (
                  <div className={`flex flex-wrap ${isMobile ? 'justify-center' : ''} items-center gap-4 mt-2`}>
                    <motion.div 
                      whileHover={{ scale: 1.05 }}
                      className={`${isMobile ? 'text-base' : 'text-lg'} font-semibold text-purple-600 bg-purple-50 px-4 py-2 rounded-lg shadow-sm border border-purple-100 flex items-center gap-2`}
                    >
                      <span className="text-purple-500">💰</span>
                      {store.cashback_value} Cashback
                    </motion.div>
                    <div className="flex flex-wrap items-center gap-2">
                      {store.track_url && (
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => store.track_url && window.open(store.track_url, '_blank')}
                          className={`inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 ${isMobile ? 'w-full justify-center mt-2' : ''}`}
                          style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                        >
                          <FaExternalLinkAlt className="mr-2 h-4 w-4" />
                          Activate Cashback
                        </motion.button>
                      )}
                      {/* Share & Earn按钮 - 在移动端和非移动端都显示 */}
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowShareModal(true)}
                        className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-xs font-medium text-gray-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-gray-400 transition-all duration-300 ${isMobile ? 'w-full justify-center mt-2' : ''}`}
                        style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                      >
                        <FaShare className="mr-2 h-3 w-3" />
                        Share & Earn
                      </motion.button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Content Sections */}
        <div className="space-y-8">
          {/* Store Description - Only if not shown in header */}
          {store.description && store.description.length > 150 && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-all duration-300"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                <FaInfoCircle className="mr-2 text-purple-500" />
                About <span className="text-purple-600">{store.name}</span>
              </h2>
              <p className="text-gray-600">{store.description}</p>
            </motion.div>
          )}

          {/* Coupons */}
          {coupons.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-white rounded-xl shadow-md p-4 md:p-6 hover:shadow-lg transition-all duration-300 mb-6"
            >
              <div className="flex flex-wrap items-center justify-between mb-4 md:mb-6">
                <div className="flex items-center">
                  <span className="inline-block w-8 h-8 md:w-10 md:h-10 rounded-full bg-purple-100 flex items-center justify-center mr-2 md:mr-3">
                    <svg className="w-4 h-4 md:w-5 md:h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </span>
                  <h2 className="text-lg md:text-xl font-bold text-gray-900">{isMobile ? '' : 'Available'} <span className="text-purple-600">{store.name}</span> {isMobile ? '' : 'Coupons'}</h2>
                </div>
                <span className="text-xs md:text-sm text-gray-500 flex items-center mt-2 md:mt-0">
                  <FaRegClock className="mr-1" /> Updated {new Date().toLocaleDateString()}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 mb-4">
                {coupons.map((coupon, index) => (
                  <motion.div 
                    key={coupon.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="coupon-card group hover:shadow-lg hover:border-purple-200 transition-all duration-300 rounded-xl"
                  >
                  <div className="coupon-content">
                    <div className="coupon-header">
                        <span className="coupon-type bg-purple-50 text-purple-600 px-2 py-1 rounded-md text-xs">
                          COUPON CODE
                        </span>
                        {store.cashback_value && (
                          <span className="cashback-badge flex items-center">
                            <span className="mr-1">💰</span>
                            {store.cashback_value} Cashback
                          </span>
                        )}
                        {coupon.ended_at && (
                          <span className="text-xs text-gray-500 flex items-center ml-auto">
                            <FaRegClock className="mr-1" />
                            Expires: {new Date(coupon.ended_at).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      
                      <h3 className="coupon-title font-bold mt-2 line-clamp-2">
                        {coupon.title}
                      </h3>
                      
                      {coupon.description && !isMobile && (
                        <p className="text-gray-600 text-sm line-clamp-2 mt-1">
                          {coupon.description}
                        </p>
                    )}
                  </div>

                  <div className="coupon-code-section">
                      <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        className="coupon-code-button w-full"
                        onClick={() => handleCouponClick(coupon)}
                        title="Click to copy code and open store"
                        style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                      >
                        <span className="code-text flex-1 text-center">
                          {maskCouponCode(coupon.code)}
                        </span>
                        <span className="get-code-text bg-gradient-to-r from-purple-500 to-pink-600 whitespace-nowrap">
                          Get Code
                        </span>
                      </motion.button>
                  </div>
                  </motion.div>
              ))}
            </div>
              
              {coupons.length > (isMobile ? 4 : 10) && (
                <div className="flex justify-center mt-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="text-purple-600 font-medium px-4 py-2 rounded-lg border border-purple-200 hover:bg-purple-50 transition-all duration-200 text-sm"
                  >
                    View All Coupons
                  </motion.button>
                </div>
              )}
            </motion.div>
          )}

          {/* Similar Stores */}
          {similarStores.length > 0 && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-white rounded-xl shadow-md p-4 md:p-6 mt-6"
            >
              <div className="flex items-center justify-between mb-4 md:mb-6">
                <div className="flex items-center">
                  <span className="inline-block w-8 h-8 md:w-10 md:h-10 rounded-full bg-blue-100 flex items-center justify-center mr-2 md:mr-3">
                    <svg className="w-4 h-4 md:w-5 md:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </span>
                <h2 className="text-lg md:text-xl font-bold text-gray-900">{isMobile ? 'Similar Stores' : 'Similar Stores to'} <span className="text-purple-600">{!isMobile && store.name}</span></h2>
                </div>
                {!isMobile && <div className="text-sm text-gray-500">Find more cashback opportunities</div>}
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4">
                {similarStores.map((similarStore, index) => (
                  <motion.div
                    key={similarStore.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: 0.1 + index * (isMobile ? 0.03 : 0.05) }}
                    whileHover={{ scale: isMobile ? 1.02 : 1.03, y: isMobile ? -2 : -5 }}
                    whileTap={{ scale: 0.98 }}
                    className="bg-white rounded-xl shadow-sm p-3 md:p-4 cursor-pointer border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-300"
                    onClick={() => navigate(`/stores/${similarStore.unique_name}`)}
                    style={{ minHeight: isMobile ? '70px' : 'auto' }} // 确保移动端有足够的触摸区域
                  >
                    <div className="flex items-center gap-3 md:gap-4">
                      <div className="w-12 h-12 md:w-16 md:h-16 bg-white rounded-lg overflow-hidden p-2 border border-gray-100 flex-shrink-0">
                        <img
                          src={similarStore.logo || '/images/default-logo.png'}
                          alt={similarStore.name}
                          className="w-full h-full object-contain rounded-lg"
                          loading="lazy"
                          onError={(e) => {
                            e.currentTarget.src = '/images/default-logo.png';
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm md:text-base font-semibold text-gray-900 truncate mb-1">{similarStore.name}</div>
                        <div className="inline-flex items-center px-2 py-1 rounded-md text-xs md:text-sm font-medium bg-purple-50 text-purple-600">
                          <span className="mr-1">💰</span>
                          {similarStore.cashback_value}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Track URL Modal */}
      <AnimatePresence>
      {showTrackUrlModal && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="modal-overlay fixed inset-0 flex items-center justify-center p-4 z-50 bg-black bg-opacity-50 backdrop-blur-sm"
              onClick={() => setShowTrackUrlModal(false)}
          >
            <motion.div 
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className="modal relative bg-white rounded-2xl shadow-2xl max-w-md w-full overflow-hidden"
              onClick={e => e.stopPropagation()}
            >
              <div className="h-2 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-400"></div>
              <button 
                className="close-button absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
                onClick={() => setShowTrackUrlModal(false)}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            
              <div className="modal-content p-6">
                <div className="merchant-info flex flex-col items-center text-center mb-6">
                  <div className="w-20 h-20 bg-white rounded-xl shadow-sm overflow-hidden p-2 mb-4 border border-gray-100">
                <img 
                  src={store.logo || '/images/default-logo.png'} 
                  alt={store.name}
                      className="w-full h-full object-contain"
                      onError={(e) => {
                        e.currentTarget.src = '/images/default-logo.png';
                      }}
                    />
                  </div>
                  <h2 className="text-xl font-bold text-gray-900 mb-2"><span className="text-purple-600">{store.name}</span></h2>
                  {store.cashback_value && (
                    <div className="cashback-badge inline-flex items-center px-3 py-1 rounded-full text-sm">
                      <span className="mr-1">💰</span>
                      {store.cashback_value} Cashback
                  </div>
                )}
              </div>

              <div className="share-info">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Cashback Activation Link</h3>
                <div className="share-url">
                    <label className="track-url-label block text-sm font-medium text-gray-700 mb-1">Track URL</label>
                    <div className="flex">
                  <input 
                    type="text" 
                    value={store.track_url || ''} 
                    readOnly 
                        className="track-url-input flex-1 rounded-l-lg"
                  />
                  <button
                        className="copy-button bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-r-lg px-4 py-2"
                    onClick={() => window.open(store.track_url || '', '_blank')}
                  >
                        <span className="flex items-center">
                          <FaExternalLinkAlt className="mr-2" />
                    Go to Store
                        </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
            </motion.div>
          </motion.div>
      )}
      </AnimatePresence>

      {/* Share Modal - 移动端优化 */}
      <AnimatePresence>
      {showShareModal && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="modal-overlay fixed inset-0 flex items-center justify-center p-4 z-50 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => setShowShareModal(false)}
          >
            <motion.div 
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className={`modal relative bg-white rounded-2xl shadow-2xl ${isMobile ? 'w-full max-w-sm' : 'max-w-md w-full'} overflow-hidden`}
              onClick={e => e.stopPropagation()}
            >
              <div className="h-2 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-400"></div>
              <button 
                className="close-button absolute right-4 top-4 text-gray-400 hover:text-gray-600 transition-colors"
                onClick={() => setShowShareModal(false)}
              >
                <svg className="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            
              <div className="modal-content p-4 md:p-6">
                <div className="merchant-info flex flex-col items-center text-center mb-4 md:mb-6">
                  <div className="w-16 h-16 md:w-20 md:h-20 bg-white rounded-xl shadow-sm overflow-hidden p-2 mb-3 md:mb-4 border border-gray-100">
                <img 
                  src={store?.logo || '/images/default-logo.png'} 
                  alt={store?.name || 'Store'}
                  className="w-full h-full object-contain"
                  loading="lazy"
                  onError={(e) => {
                    e.currentTarget.src = '/images/default-logo.png';
                  }}
                    />
                  </div>
                  <h2 className="text-lg md:text-xl font-bold text-gray-900 mb-1 md:mb-2"><span className="text-purple-600">{store?.name}</span></h2>
                  {store?.cashback_value && (
                    <div className="cashback-badge inline-flex items-center px-3 py-1 rounded-full text-sm">
                      <span className="mr-1">💰</span>
                      {store.cashback_value} Cashback
                  </div>
                )}
              </div>

              <div className="share-info">
                <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-3 md:mb-4 text-center">Share Your Link, Earn a Commission</h3>
                <div className="share-url space-y-3 md:space-y-4">
                    <div>
                      <label className="block text-xs md:text-sm font-medium text-gray-700 mb-1">Tracking Code (Optional)</label>
                  <input 
                    type="text" 
                    placeholder="Enter tracking code (max 5 chars)"
                    value={sub1} 
                    onChange={handleSub1Change}
                    className={`share-input w-full rounded-lg ${sub1Error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-purple-500 focus:border-purple-500'}`}
                  />
                  {sub1Error && (
                        <div className="text-red-500 text-xs md:text-sm mt-1">{sub1Error}</div>
                  )}
                    </div>
                    
                    <div>
                      <label className="block text-xs md:text-sm font-medium text-gray-700 mb-1">Your Affiliate Link</label>
                      <div className="flex">
                  <input 
                    type="text" 
                    value={shareUrl} 
                    readOnly 
                    className="share-input flex-1 rounded-l-lg text-xs md:text-sm"
                  />
                  <button
                    className="copy-button bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-r-lg px-3 md:px-4 py-2 flex items-center text-xs md:text-sm"
                    onClick={handleShare}
                    style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                  >
                    <FaCopy className="mr-1 md:mr-2" />
                    Copy
                  </button>
                </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 md:mt-6 pt-3 md:pt-4 border-t border-gray-200">
                    <p className="text-xs md:text-sm text-gray-600 mb-3 text-center">Share on social media</p>
                  <div className="flex items-center justify-center gap-3 md:gap-4">
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        whileTap={{ scale: 0.9 }}
                      onClick={() => window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${generateShareText()}`, '_blank')}
                        className="p-2 md:p-3 rounded-full bg-[#1877F2] text-white hover:bg-[#0d6efd] transition-colors"
                      title="Share on Facebook"
                    >
                        <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                      </svg>
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: -5 }}
                        whileTap={{ scale: 0.9 }}
                      onClick={() => window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}&text=${generateShareText()}`, '_blank')}
                        className="p-2 md:p-3 rounded-full bg-black text-white hover:bg-gray-800 transition-colors"
                      title="Share on X (Twitter)"
                    >
                        <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                      </svg>
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
      )}
      </AnimatePresence>

      {/* Coupon Modal - 移动端优化 */}
      <AnimatePresence>
      {selectedCoupon && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="modal-overlay fixed inset-0 flex items-center justify-center p-3 md:p-4 z-50 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => {
          if (!isCopied) {
            setSelectedCoupon(null)
          }
            }}
          >
            <motion.div 
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className={`modal relative bg-white rounded-2xl shadow-2xl ${isMobile ? 'w-full max-w-sm' : 'max-w-md w-full'} overflow-hidden`}
              onClick={e => e.stopPropagation()}
            >
              <div className="h-2 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-400"></div>
            <button 
                className="close-button absolute right-3 md:right-4 top-3 md:top-4 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={() => {
                if (!isCopied) {
                  setSelectedCoupon(null)
                }
              }}
              disabled={isCopied}
            >
                <svg className="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            
              <div className="modal-content p-4 md:p-6">
                <div className="merchant-info flex flex-col items-center text-center mb-4 md:mb-6">
                  <div className="w-16 h-16 md:w-20 md:h-20 bg-white rounded-xl shadow-sm overflow-hidden p-2 mb-3 md:mb-4 border border-gray-100">
                <img 
                  src={store?.logo || '/images/default-logo.png'} 
                  alt={store?.name || 'Store'}
                  className="w-full h-full object-contain"
                  loading="lazy"
                  onError={(e) => {
                    e.currentTarget.src = '/images/default-logo.png';
                  }}
                    />
                  </div>
                  <h2 className="text-lg md:text-xl font-bold text-gray-900 mb-1 md:mb-2">
                    <span className="text-purple-600">{store?.name}</span>
                  </h2>
                  {store?.cashback_value && (
                    <div className="cashback-badge inline-flex items-center px-3 py-1 rounded-full text-xs md:text-sm">
                      <span className="mr-1">💰</span>
                      {store.cashback_value} Cashback
                    </div>
                  )}
                </div>

                <div className="coupon-info">
                  <h3 className="coupon-title text-center text-base md:text-lg font-bold text-gray-900 mb-3 md:mb-4">{selectedCoupon.title}</h3>
                  
                  <div className="code-section bg-gray-50 rounded-xl p-3 md:p-4 mb-4 md:mb-6">
                    <div className="code-display flex flex-col items-center">
                      <div className="text-xs text-gray-500 mb-2">Your Coupon Code:</div>
                      <div className="coupon-code-container bg-white border-2 border-dashed border-purple-300 rounded-lg p-2 md:p-3 mb-3 w-full text-center relative">
                        <code className="coupon-code text-base md:text-lg font-mono font-bold text-gray-800">
                      {selectedCoupon.code}
                    </code>
                  </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="copy-button w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white rounded-lg px-4 py-3 font-medium flex items-center justify-center"
                        onClick={() => handleCopy(selectedCoupon.code)}
                        disabled={isCopied}
                        style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                      >
                    {countdown 
                          ? <span className="countdown flex items-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Opening store in {countdown}s...
                            </span>
                          : (
                            <span className="flex items-center text-sm md:text-base">
                              <FaCopy className="mr-2" />
                              Copy Code & Open Store
                            </span>
                          )
                        }
                      </motion.button>
                    </div>
                </div>

                  <div className="instructions bg-blue-50 rounded-xl p-3 md:p-4 mb-4 md:mb-6">
                    <h4 className="font-semibold text-blue-800 mb-2 text-sm md:text-base">How to use this coupon:</h4>
                    <ol className="text-blue-700 text-xs md:text-sm space-y-1 md:space-y-2 ml-4 md:ml-5 list-decimal">
                    <li>Click "Copy Code & Open Store" button above</li>
                    <li>Wait 3 seconds for the store to open in a new tab</li>
                    <li>Add items to your cart</li>
                    <li>Paste the code at checkout</li>
                  </ol>
                </div>

                {selectedCoupon.description && (
                    <div className="description bg-gray-50 rounded-xl p-3 md:p-4 mb-4 md:mb-6">
                      <h4 className="font-semibold text-gray-800 mb-1 md:mb-2 text-sm md:text-base">Details:</h4>
                      <p className="text-gray-600 text-xs md:text-sm">{selectedCoupon.description}</p>
                  </div>
                )}

                <div className="action-buttons">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="shop-now-button w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-3 font-medium flex items-center justify-center text-sm md:text-base"
                      onClick={() => window.open(store?.track_url || '', '_blank')}
                      style={{ minHeight: isMobile ? '44px' : 'auto' }} // 增大移动端触摸区域
                    >
                      <FaExternalLinkAlt className="mr-2" />
                    Shop Now
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
        </div>
  )
}

export default StoreDetail
