import React, { useEffect, useState, useCallback, useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useStore } from '../../contexts/StoreContext'
import StoreCard from '../../components/common/StoreCard'
import { Store, Category } from '../../types'
import { motion } from 'framer-motion'
import { FaFilter, FaChevronRight, FaAngleDown, FaAngleUp, FaBars, FaTimes } from 'react-icons/fa'
import * as FaIcons from 'react-icons/fa'

// Generate alphabet array
const alphabet = Array.from('ABCDEFGHIJKLMNOPQRSTUVWXYZ')
const numbers = ['0-9']
const filterOptions = ['', ...alphabet, ...numbers]

const StoresPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { getStores, getCategories } = useStore()

  const [stores, setStores] = useState<Store[]>([])
  const [featuredStores, setFeaturedStores] = useState<Store[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(true)
  const [featuredLoading, setFeaturedLoading] = useState(true)
  const [isMobile, setIsMobile] = useState(false)
  const [showSidebar, setShowSidebar] = useState(false)

  // 使用 ref 来跟踪请求状态
  const requestInProgress = useRef(false)
  const categoriesLoaded = useRef(false)

  // 检测设备类型
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
    }

    checkIfMobile()
    window.addEventListener('resize', checkIfMobile)
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Get query params
  const searchParams = new URLSearchParams(location.search)
  const page = +(searchParams.get('page') || '1')
  const categoryId = searchParams.get('category_id')
  const letter = searchParams.get('starts_with')
  const search = searchParams.get('search')
  const pageSize = 90

  // 新增: 控制分类显示数量的状态
  const [showAllCategories, setShowAllCategories] = useState(false)
  const [hoveredCategory, setHoveredCategory] = useState<number | null>(null)
  const visibleCategoriesCount = 10 // 默认显示的分类数量

  // 通过筛选条件获取需要显示的分类
  const getVisibleCategories = () => {
    const filteredCategories = categories.filter(cat => {
      // 如果有搜索词，则过滤分类
      if (categorySearch && !cat.name.toLowerCase().includes(categorySearch.toLowerCase())) {
        return false;
      }
      return true;
    });

    if (showAllCategories) {
      return filteredCategories;
    }
    return filteredCategories.slice(0, visibleCategoriesCount);
  };

  // 获取动态渲染图标组件的函数
  const getCategoryIcon = (iconName: string | undefined) => {
    if (!iconName) return null;
    const IconComponent = (FaIcons as any)[iconName];
    return IconComponent ? <IconComponent className="w-4 h-4" /> : null;
  };

  // 新增: 控制分类搜索
  const [categorySearch, setCategorySearch] = useState("");

  // 移动端点击分类后关闭侧边栏
  const handleMobileCategoryClick = (categoryId?: string) => {
    navigate(
      buildUrl(1, {
        category_id: categoryId || '',
      })
    )
    if (isMobile) {
      setShowSidebar(false)
    }
  }

  // 处理筛选选项
  const handleFilterOption = (option: string) => {
    if (option === '') {
      // 清除字母筛选时，保持其他筛选条件不变
      return buildUrl(1, {
        starts_with: ''
      })
    }
    if (option === '0-9') {
      // 对于0-9选项，使用特殊的查询参数
      return buildUrl(1, {
        starts_with: 'number'
      })
    }
    return buildUrl(1, {
      starts_with: option
    })
  }

  // 只获取一次分类数据
  const fetchCategories = useCallback(async () => {
    if (categoriesLoaded.current) return

    try {
      const data = await getCategories()
      setCategories(data || [])
      categoriesLoaded.current = true
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }, [getCategories])

  // Fetch featured stores
  const fetchFeaturedStores = useCallback(async () => {
    setFeaturedLoading(true)
    try {
      const response = await getStores({
        page: 1,
        page_size: 12,
        featured: true
      })

      if (response && Array.isArray(response.merchant_list)) {
        setFeaturedStores(response.merchant_list)
      } else {
        console.error('Invalid response format for featured stores:', response)
        setFeaturedStores([])
      }
    } catch (error) {
      console.error('Error fetching featured stores:', error)
      setFeaturedStores([])
    } finally {
      setFeaturedLoading(false)
    }
  }, [getStores])

  // 获取商店数据
  const fetchStores = useCallback(async () => {
    if (requestInProgress.current) return

    requestInProgress.current = true
    setLoading(true)

    try {
      const params: Record<string, any> = {
        page,
        page_size: pageSize,
      }

      if (categoryId) {
        params.category_id = categoryId
      }

      if (letter) {
        params.starts_with = letter
      }

      if (search) {
        params.search = search
      }

      const response = await getStores(params)

      if (response && Array.isArray(response.merchant_list)) {
        setStores(response.merchant_list)
        // 使用后端返回的total字段
        setTotal(response.total)
      } else {
        console.error('Invalid response format:', response)
        setStores([])
        setTotal(0)
      }
    } catch (error) {
      console.error('Error fetching stores:', error)
      setStores([])
      setTotal(0)
    } finally {
      setLoading(false)
      requestInProgress.current = false
    }
  }, [getStores, page, categoryId, letter, search])

  // 监听 URL 参数变化，重新获取数据
  useEffect(() => {
    fetchStores()
  }, [fetchStores])

  // 获取分类数据
  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  // Fetch featured stores on initial load
  useEffect(() => {
    fetchFeaturedStores()
  }, [fetchFeaturedStores])

  // Build URL helper function
  const buildUrl = useCallback((newPage?: number, newParams?: Record<string, string>) => {
    const url = new URLSearchParams(location.search)
    if (newPage) {
      url.set('page', newPage.toString())
    }
    if (newParams) {
      Object.entries(newParams).forEach(([key, value]) => {
        if (value) {
          url.set(key, value)
        } else {
          url.delete(key)
        }
      })
    }
    return '?' + url.toString()
  }, [location.search])

  // 生成分页按钮数组
  const generatePaginationArray = useCallback((currentPage: number, totalPages: number) => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];
    let l;

    range.push(1);

    for (let i = currentPage - delta; i <= currentPage + delta; i++) {
      if (i < totalPages && i > 1) {
        range.push(i);
      }
    }

    range.push(totalPages);

    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push('...');
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  }, []);

  // Current filters for display
  const currentFilters: string[] = []
  if (search) currentFilters.push(`Search: ${search}`)
  if (categoryId) {
    const category = categories.find((c) => c.id.toString() === categoryId)
    if (category) currentFilters.push(`Category: ${category.name}`)
  }
  if (letter) currentFilters.push(`Starting with: ${letter}`)

  return (
    <main className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-2 py-4 md:py-6 max-w-[1800px]">
        {/* Mobile Header - Enhanced */}
        {isMobile && (
          <div className="sticky top-0 z-40 bg-gradient-to-r from-purple-600 to-purple-800 shadow-md rounded-xl mb-4 px-4 py-3.5 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Explore Stores</h2>
            <button
              onClick={() => setShowSidebar(!showSidebar)}
              className="p-2 rounded-md bg-white/20 text-white backdrop-blur-sm hover:bg-white/30 transition-colors duration-200"
            >
              {showSidebar ? <FaTimes /> : <FaFilter />}
            </button>
          </div>
        )}

        <div className="flex gap-4 relative">
          {/* Enhanced Sidebar - Categories */}
          <div
            className={`${isMobile ? 'fixed inset-0 z-30 transform transition-transform duration-300 ease-in-out bg-gray-900/50 backdrop-blur-sm' : 'w-64 flex-shrink-0'}
                      ${isMobile && !showSidebar ? '-translate-x-full opacity-0 pointer-events-none' : 'translate-x-0 opacity-100'}`}
          >
            <div
              className={`${isMobile ? 'h-full w-[280px] overflow-auto' : 'sticky top-4'} bg-white rounded-xl shadow-md p-5 border border-gray-100`}
            >
              <div className="flex items-center justify-between mb-5">
                <div className="flex items-center">
                  <div className="w-1 h-5 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-2.5"></div>
                  <h2 className="text-lg font-bold text-gray-800">Categories</h2>
                </div>
                {isMobile && (
                  <button
                    onClick={() => setShowSidebar(false)}
                    className="p-1.5 rounded-md text-gray-500 hover:text-purple-600 hover:bg-purple-50 transition-colors duration-200"
                  >
                    <FaTimes />
                  </button>
                )}
                {!isMobile && <FaFilter className="text-purple-500" />}
              </div>

              {/* Enhanced Category Search */}
              <div className="mb-4 relative">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    className="w-full pl-10 pr-10 py-2.5 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
                    placeholder="Search categories..."
                    value={categorySearch}
                    onChange={(e) => setCategorySearch(e.target.value)}
                  />
                  {categorySearch && (
                    <button
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-500"
                      onClick={() => setCategorySearch("")}
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>

              <div
                className="overflow-hidden"
                style={{
                  maxHeight: isMobile ? "calc(100vh - 200px)" : "calc(100vh - 200px)",
                }}
              >
                <div
                  className="pr-2 overflow-y-auto custom-scrollbar pb-1"
                  style={{
                    maxHeight: isMobile ? "calc(100vh - 200px)" : "calc(100vh - 200px)",
                    scrollbarWidth: "thin",
                    scrollbarColor: "#9333ea #f1f5f9",
                  }}
                >
                  {/* 滚动指示器 - 在非移动端显示 */}
                  {!isMobile && (
                    <div className="absolute top-[132px] right-4 w-1 h-16 bg-gray-100 rounded-full opacity-60 pointer-events-none">
                      <motion.div
                        className="w-1 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"
                        initial={{ height: "0%" }}
                        animate={{ height: "30%" }}
                        transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse", ease: "easeInOut" }}
                      />
                    </div>
                  )}

                  <motion.button
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3 }}
                    onClick={() => handleMobileCategoryClick()}
                    className={`group flex items-center w-full px-4 py-2.5 rounded-lg text-sm transition-all duration-300 text-left relative overflow-hidden ${
                      !categoryId
                        ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm hover:shadow-md hover:from-purple-700 hover:to-pink-700'
                        : 'bg-gray-50 hover:bg-gray-100 text-gray-700 hover:pl-6'
                    }`}
                  >
                    <span className="relative z-10 flex-1">All Categories</span>
                    {!categoryId && <FaChevronRight className="relative z-10 w-3 h-3 text-white" />}
                    <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-400 to-pink-400 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300 ease-out opacity-0 group-hover:opacity-10"></div>
                  </motion.button>

                  {getVisibleCategories().map((category, index) => (
                    <motion.button
                      key={category.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: isMobile ? 0.01 * index : 0.03 * index }}
                      onClick={() => handleMobileCategoryClick(category.id.toString())}
                      onMouseEnter={() => !isMobile && setHoveredCategory(category.id)}
                      onMouseLeave={() => !isMobile && setHoveredCategory(null)}
                      className={`group flex items-center w-full px-4 py-2.5 rounded-lg text-sm transition-all duration-300 text-left relative overflow-hidden mt-1.5 ${
                        categoryId === category.id.toString()
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm hover:shadow-md hover:from-purple-700 hover:to-pink-700'
                          : 'bg-gray-50 hover:bg-gray-100 text-gray-700 hover:pl-6'
                      } ${hoveredCategory === category.id && !isMobile ? 'scale-[1.02]' : ''}`}
                      whileHover={{ x: isMobile ? 0 : 3 }}
                    >
                      <span className="relative z-10 flex items-center gap-2 flex-1">
                        <motion.span
                          className="w-5 h-5 flex items-center justify-center opacity-80"
                          animate={{
                            rotate: hoveredCategory === category.id && !isMobile ? [0, 5, 0, -5, 0] : 0,
                          }}
                          transition={{ duration: 0.5, repeat: hoveredCategory === category.id && !isMobile ? Infinity : 0, repeatDelay: 0.5 }}
                        >
                          {getCategoryIcon(category.icon)}
                        </motion.span>
                        <span className="truncate">{category.name}</span>
                      </span>
                      {categoryId === category.id.toString() && (
                        <motion.span
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 300, damping: 20 }}
                        >
                          <FaChevronRight className="relative z-10 w-3 h-3 text-white" />
                        </motion.span>
                      )}
                      <div className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-400 to-pink-400 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300 ease-out opacity-0 group-hover:opacity-10"></div>
                    </motion.button>
                  ))}

                  {categories.length > visibleCategoriesCount && !showAllCategories && !categorySearch && (
                    <motion.button
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: isMobile ? 0.2 : 0.5 }}
                      onClick={() => setShowAllCategories(true)}
                      className="group w-full mt-3 px-4 py-2.5 rounded-lg text-sm transition-all duration-200 bg-gray-50 hover:bg-gray-100 text-purple-600 hover:text-purple-700 flex items-center justify-center border border-gray-100 hover:border-purple-200"
                    >
                      <span>Show all categories</span>
                      <FaAngleDown className="ml-1 w-4 h-4" />
                    </motion.button>
                  )}

                  {showAllCategories && !categorySearch && (
                    <motion.button
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      onClick={() => setShowAllCategories(false)}
                      className="group w-full mt-3 px-4 py-2.5 rounded-lg text-sm transition-all duration-200 bg-gray-50 hover:bg-gray-100 text-purple-600 hover:text-purple-700 flex items-center justify-center border border-gray-100 hover:border-purple-200"
                    >
                      <span>Show less</span>
                      <FaAngleUp className="ml-1 w-4 h-4" />
                    </motion.button>
                  )}

                  {getVisibleCategories().length === 0 && (
                    <div className="py-4 text-center text-gray-500 text-sm">
                      No matching categories found
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 主内容 */}
          <div className="flex-1 space-y-4 md:space-y-6">
            {/* Enhanced Header with Rich Background */}
            <div className="bg-gradient-to-r from-purple-700 to-purple-900 rounded-xl shadow-lg p-6 md:p-8 relative overflow-hidden">
              {/* Decorative Background Elements */}
              <div className="absolute inset-0 overflow-hidden">
                {/* Animated Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/30 via-purple-800/20 to-pink-600/30"></div>

                {/* Decorative Circles */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-pink-500/20 to-purple-500/20 rounded-full blur-3xl transform translate-x-1/3 -translate-y-1/3"></div>
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-full blur-3xl transform -translate-x-1/3 translate-y-1/3"></div>

                {/* Dot Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
                    <defs>
                      <pattern id="pattern-circles" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse">
                        <circle id="pattern-circle" cx="10" cy="10" r="1.6257413380501518" fill="#ffffff" />
                      </pattern>
                    </defs>
                    <rect x="0" y="0" width="100%" height="100%" fill="url(#pattern-circles)" />
                  </svg>
                </div>
              </div>

              <div className="relative z-10 max-w-3xl mx-auto text-center">
                <motion.div
                  className="inline-block px-4 py-1 rounded-full bg-white/20 backdrop-blur-sm mb-4"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <span className="text-white/90 text-sm font-medium">Discover & Earn</span>
                </motion.div>

                <motion.h1
                  className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 text-white"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  Explore All Stores
                </motion.h1>

                <motion.p
                  className="text-center text-white/80 text-base md:text-lg mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  Find your favorite brands and earn cashback on every purchase
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <div className="flex flex-wrap justify-center gap-2 text-sm text-white/70">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Verified Merchants</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Exclusive Cashback</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Limited-Time Offers</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Featured Merchants Section */}
            {!featuredLoading && featuredStores.length > 0 && (
              <motion.div
                className="bg-white rounded-xl shadow-sm p-4 md:p-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="flex items-center mb-4">
                  <div className="w-1 h-6 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-3"></div>
                  <h2 className="text-xl font-bold text-gray-800">Featured Merchants</h2>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4">
                  {featuredStores.map((store) => (
                    <div
                      key={store.id}
                      className="group"
                    >
                      <div className="h-full transform hover:-translate-y-2 transition-all duration-300 hover:shadow-md rounded-xl overflow-hidden">
                        <StoreCard store={store} />
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Active Filters - Enhanced */}
            {currentFilters.length > 0 && (
              <motion.div
                className="bg-white rounded-xl shadow-sm p-4 md:p-5"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.5 }}
              >
                <div className="flex flex-wrap items-center gap-2">
                  <div className="flex items-center">
                    <div className="w-1 h-4 bg-purple-400 rounded-full mr-2"></div>
                    <span className="text-gray-700 text-xs md:text-sm font-medium">Active Filters:</span>
                  </div>

                  {currentFilters.map((filter, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1.5 rounded-full text-xs md:text-sm font-medium bg-purple-50 text-purple-700 border border-purple-100 shadow-sm"
                    >
                      {filter}
                    </span>
                  ))}

                  <button
                    onClick={() => {
                      navigate('/stores')
                    }}
                    className="ml-auto flex items-center text-purple-600 hover:text-purple-800 text-xs md:text-sm font-medium transition-colors duration-200"
                  >
                    <span>Clear All</span>
                    <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </motion.div>
            )}

            {/* Alphabet Filter - Enhanced */}
            <motion.div
              className="bg-white rounded-xl shadow-sm p-4 md:p-5"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.6 }}
            >
              <div className="flex items-center mb-3">
                <div className="w-1 h-4 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full mr-2"></div>
                <h2 className="text-sm md:text-base font-medium text-gray-800">Filter by Letter</h2>
              </div>

              <div className="flex flex-wrap gap-1.5 md:gap-2">
                {filterOptions.map((option) => {
                  const isActive = (option === '' && !letter) ||
                                  (option === letter) ||
                                  (option === '0-9' && letter === 'number');

                  return (
                    <button
                      key={option || 'all'}
                      className={`min-w-[32px] md:min-w-[40px] h-9 md:h-10 flex items-center justify-center rounded-lg text-xs md:text-sm font-medium transition-all duration-200 ${
                        isActive
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm hover:shadow-md'
                          : 'bg-gray-50 text-gray-700 hover:bg-purple-50 hover:text-purple-700 border border-gray-100'
                      }`}
                      onClick={() => navigate(handleFilterOption(option))}
                    >
                      {option === '' ? 'All' : option}
                    </button>
                  );
                })}
              </div>
            </motion.div>

            {/* All Stores Section - Enhanced */}
            <div className="bg-white rounded-xl shadow-sm p-4 md:p-6">
              <div className="flex items-center mb-4">
                <div className="w-1 h-6 bg-gradient-to-b from-purple-600 to-pink-600 rounded-full mr-3"></div>
                <h2 className="text-xl font-bold text-gray-800">All Stores</h2>
                {!loading && stores.length > 0 && (
                  <span className="ml-3 px-2.5 py-1 bg-purple-50 text-purple-700 text-xs font-medium rounded-full">
                    {total} {total === 1 ? 'Store' : 'Stores'}
                  </span>
                )}
              </div>

              {loading ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4 animate-pulse">
                  {[...Array(isMobile ? 6 : 18)].map((_, index) => (
                    <div key={index} className="h-48 md:h-60 bg-gray-100 rounded-xl">
                      <div className="h-2/3 bg-gray-200 rounded-t-xl"></div>
                      <div className="h-1/3 p-3">
                        <div className="w-3/4 h-3 bg-gray-200 rounded mb-2"></div>
                        <div className="w-1/2 h-3 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : stores.length > 0 ? (
                <motion.div
                  className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  {stores.map((store) => (
                    <div
                      key={store.id}
                      className="group"
                    >
                      <div className="h-full transform hover:-translate-y-2 transition-all duration-300 hover:shadow-md rounded-xl overflow-hidden">
                        <StoreCard store={store} />
                      </div>
                    </div>
                  ))}
                </motion.div>
              ) : (
                <div className="text-center py-10 md:py-16 bg-gray-50 rounded-xl">
                  <div className="inline-flex items-center justify-center w-16 h-16 md:w-20 md:h-20 bg-purple-50 rounded-full mb-4">
                    <svg className="w-8 h-8 md:w-10 md:h-10 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">No Stores Found</h3>
                  <p className="text-sm md:text-base text-gray-600 mb-6 max-w-md mx-auto">
                    We couldn't find any stores matching your current filters. Try adjusting your search criteria.
                  </p>
                  <button
                    onClick={() => {
                      navigate('/stores')
                    }}
                    className="inline-flex items-center px-5 md:px-6 py-2 md:py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-200 hover:shadow-md focus:outline-none"
                  >
                    <span>View All Stores</span>
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                </div>
              )}
            </div>

            {/* Enhanced Pagination */}
            {total > pageSize && (
              <motion.div
                className="flex justify-center py-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.7 }}
              >
                <div className="flex space-x-2 rounded-xl bg-white p-3 shadow-sm border border-gray-100">
                  <button
                    onClick={() => {
                      if (page > 1) {
                        navigate(buildUrl(page - 1))
                      }
                    }}
                    disabled={page <= 1}
                    className={`inline-flex h-10 md:h-11 items-center justify-center rounded-lg px-3 md:px-4 text-xs md:text-sm font-medium transition-all duration-200 ${
                      page <= 1
                        ? 'pointer-events-none text-gray-300 bg-gray-50'
                        : 'text-gray-700 hover:bg-purple-50 hover:text-purple-700 border border-gray-100'
                    }`}
                    style={{ minHeight: isMobile ? '44px' : 'auto' }}
                  >
                    <svg className="h-4 w-4 md:h-5 md:w-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    {!isMobile && "Previous"}
                  </button>

                  <div className="hidden sm:flex sm:items-center sm:space-x-1.5">
                    {generatePaginationArray(page, Math.ceil(total / pageSize)).map((pageItem, index) => (
                      <div key={index}>
                        {pageItem === '...' ? (
                          <span className="inline-flex h-10 md:h-11 w-10 md:w-11 items-center justify-center text-xs md:text-sm text-gray-400">
                            ···
                          </span>
                        ) : (
                          <button
                            onClick={() => {
                              if (typeof pageItem === 'number') {
                                navigate(buildUrl(pageItem))
                              }
                            }}
                            className={`inline-flex h-10 md:h-11 w-10 md:w-11 items-center justify-center rounded-lg text-xs md:text-sm font-medium transition-all duration-200 ${
                              pageItem === page
                                ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm'
                                : 'text-gray-700 hover:bg-purple-50 hover:text-purple-700 border border-gray-100'
                            }`}
                            style={{ minHeight: isMobile ? '44px' : 'auto' }}
                          >
                            {pageItem}
                          </button>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Mobile simplified pagination */}
                  {isMobile && (
                    <div className="flex items-center px-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-700">
                        {page} / {Math.ceil(total / pageSize)}
                      </span>
                    </div>
                  )}

                  <button
                    onClick={() => {
                      if (page < Math.ceil(total / pageSize)) {
                        navigate(buildUrl(page + 1))
                      }
                    }}
                    disabled={page >= Math.ceil(total / pageSize)}
                    className={`inline-flex h-10 md:h-11 items-center justify-center rounded-lg px-3 md:px-4 text-xs md:text-sm font-medium transition-all duration-200 ${
                      page >= Math.ceil(total / pageSize)
                        ? 'pointer-events-none text-gray-300 bg-gray-50'
                        : 'text-gray-700 hover:bg-purple-50 hover:text-purple-700 border border-gray-100'
                    }`}
                    style={{ minHeight: isMobile ? '44px' : 'auto' }}
                  >
                    {!isMobile && "Next"}
                    <svg className="h-4 w-4 md:h-5 md:w-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </main>
  )
}

export default StoresPage
