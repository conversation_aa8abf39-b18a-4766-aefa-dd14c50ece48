import React, { useEffect, useState, useCallback, useMemo, Suspense, lazy } from 'react'
import { useStore } from '../contexts/StoreContext'
import { useCoupon } from '../contexts/CouponContext'
import StoreCard from '../components/common/StoreCard'
import CategoryCard from '../components/common/CategoryCard'
import CouponCard from '../components/common/CouponCard'
import LoadingAnimation from '../components/common/LoadingAnimation'
import SkeletonLoader from '../components/common/SkeletonLoader'
import { Link } from 'react-router-dom'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import {
  FaShoppingBag,
  FaShoppingCart,
  FaTshirt,
  FaLaptop,
  FaHome,
  FaBook,
  FaGamepad,
  FaGift,
  FaPlane,
  FaCar,
  FaUtensils,
  FaBriefcase,
  FaArrowUp
} from 'react-icons/fa'
import * as FaIcons from 'react-icons/fa'
import { Category, Store } from '../types'

// 移除所有缓存相关代码，确保每次都获取最新数据
// 登录用户和未登录用户的数据不同，不应该缓存

const Home = () => {
  const { getStores, getCategories } = useStore()
  const { getCoupons } = useCoupon()
  
  // 状态管理 - 将数据获取状态与UI状态分离
  const [featuredStores, setFeaturedStores] = useState<Store[]>([])
  const [popularStores, setPopularStores] = useState<Store[]>([])
  const [travelStores, setTravelStores] = useState<Store[]>([])
  const [featuredCoupons, setFeaturedCoupons] = useState<any[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  
  // 加载状态 - 为每个数据源单独设置加载状态
  const [loadingStates, setLoadingStates] = useState({
    featuredStores: true,
    popularStores: true,
    travelStores: true,
    featuredCoupons: true,
    categories: true
  })
  
  // 计算总体加载状态
  const isLoading = useMemo(() => {
    return Object.values(loadingStates).some(state => state)
  }, [loadingStates])
  
  // UI相关状态
  const [showScrollTop, setShowScrollTop] = useState(false)
  const { scrollY } = useScroll()

  // 创建平滑的滚动效果
  const categoriesScale = useTransform(scrollY, [100, 300], [0.95, 1])
  const categoriesOpacity = useTransform(scrollY, [100, 300], [0.8, 1])

  // 监听滚动位置
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 500)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // 滚动到顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Simple, elegant hero content
  const heroContent = {
    title: "Earn Cashback on Every Purchase",
    subtitle: "Shop at tens of thousands of stores and get rewarded instantly",
    buttonText: "Explore Stores",
    buttonLink: "/stores"
  }

  // 获取精选商店数据
  const fetchFeaturedStores = useCallback(async () => {
    try {
      const featuredResponse = await getStores({
        page: 1,
        page_size: 30,
        featured: true
      })
      const storesList = featuredResponse.merchant_list || []
      setFeaturedStores(storesList)
    } catch (error) {
      console.error('Error fetching featured stores:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, featuredStores: false }))
    }
  }, [getStores])

  // 获取热门商店数据
  const fetchPopularStores = useCallback(async () => {
    try {
      const popularResponse = await getStores({
        page: 1,
        page_size: 30,
        sort: 'popular'
      })
      const storesList = popularResponse.merchant_list || []
      setPopularStores(storesList)
    } catch (error) {
      console.error('Error fetching popular stores:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, popularStores: false }))
    }
  }, [getStores])

  // 获取旅行商店数据
  const fetchTravelStores = useCallback(async () => {
    try {
      const travelResponse = await getStores({
        page: 1,
        page_size: 30,
        category_id: 53
      })
      const storesList = travelResponse.merchant_list || []
      setTravelStores(storesList)
    } catch (error) {
      console.error('Error fetching travel stores:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, travelStores: false }))
    }
  }, [getStores])

  // 获取优惠券数据
  const fetchFeaturedCoupons = useCallback(async () => {
    try {
      const couponsResponse = await getCoupons({
        page: 1,
        page_size: 30
      })
      const couponsList = couponsResponse.coupon_list || []
      setFeaturedCoupons(couponsList)
    } catch (error) {
      console.error('Error fetching featured coupons:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, featuredCoupons: false }))
    }
  }, [getCoupons])

  // 获取分类数据
  const fetchCategories = useCallback(async () => {
    try {
      const categoriesData = await getCategories()
      const categoriesList = categoriesData || []
      setCategories(categoriesList)
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, categories: false }))
    }
  }, [getCategories])

  // 初始化数据获取
  useEffect(() => {
    // 并行获取所有数据，提高加载速度
    fetchFeaturedStores()
    fetchPopularStores()
    fetchTravelStores()
    fetchFeaturedCoupons()
    fetchCategories()
  }, [fetchFeaturedStores, fetchPopularStores, fetchTravelStores, fetchFeaturedCoupons, fetchCategories])

  // 渲染骨架屏的函数 - 根据不同部分显示不同的骨架屏
  const renderSkeletons = (section: string) => {
    switch (section) {
      case 'featuredStores':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
            <SkeletonLoader type="store" count={12} />
          </div>
        )
      case 'popularStores':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
            <SkeletonLoader type="store" count={12} />
          </div>
        )
      case 'travelStores':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
            <SkeletonLoader type="store" count={6} />
          </div>
        )
      case 'categories':
        return (
          <div className="flex gap-4 mt-6 overflow-hidden">
            <SkeletonLoader type="category" count={8} />
          </div>
        )
      case 'coupons':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
            <SkeletonLoader type="text" count={6} />
          </div>
        )
      default:
        return null
    }
  }

  // 处理分类图标的函数
  const renderCategoryIcon = (categoryData: Category) => {
    const { icon, name } = categoryData;

    // 1. 先尝试从icon属性获取图标组件
    if (icon) {
      const IconComponent = (FaIcons as any)[icon];
      if (IconComponent) {
        return <IconComponent className="w-6 h-6 text-purple-500" />;
      }
    }

    // 2. 检查是否是在线URL或本地图片
    if (icon && (icon.startsWith('http://') || icon.startsWith('https://') || icon.startsWith('/') || icon.includes('./'))) {
      return icon;
    }

    // 3. 根据分类名称选择合适的图标名称
    const iconMap: { [key: string]: string } = {
      'Fashion': 'FaTshirt',
      'Electronics': 'FaLaptop',
      'Home': 'FaHome',
      'Books': 'FaBook',
      'Gaming': 'FaGamepad',
      'Gifts': 'FaGift',
      'Travel': 'FaPlane',
      'Automotive': 'FaCar',
      'Food': 'FaUtensils',
      'Business': 'FaBriefcase',
      'Shopping': 'FaShoppingCart',
    };

    // 4. 尝试从映射中找到图标
    if (name && iconMap[name]) {
      const MappedIconComponent = (FaIcons as any)[iconMap[name]];
      if (MappedIconComponent) {
        return <MappedIconComponent className="w-6 h-6 text-purple-500" />;
      }
    }

    // 5. 默认返回购物袋图标
    return <FaShoppingBag className="w-6 h-6 text-purple-500" />;
  }

  const handleCouponClick = (coupon: any) => {
    // 在新标签页打开优惠券详情页
    const couponUrl = `/coupons?code=${coupon.code}`;
    window.open(couponUrl, '_blank');

    // 当前页面跳转到商家跟踪链接
    const trackUrl = coupon.merchant_info?.track_url || coupon.merchant_track_url || coupon.merchantTrackUrl;
    if (trackUrl) {
      window.location.href = trackUrl;
    }
  };

  return (
    <main className="min-h-screen bg-gray-50" aria-busy={isLoading}>
      {/* Elegant Centered Hero Section with Premium Purple Theme */}
      <div className="relative h-[600px] overflow-hidden">
        {/* Deep Premium Purple Background */}
        <div className="absolute inset-0 bg-[#4A2899]">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <motion.div
              className="absolute top-0 left-0 w-full h-full"
              animate={{
                background: [
                  'radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.15) 0%, rgba(236, 72, 153, 0) 50%)',
                  'radial-gradient(circle at 40% 70%, rgba(139, 92, 246, 0.15) 0%, rgba(236, 72, 153, 0) 50%)',
                  'radial-gradient(circle at 70% 20%, rgba(139, 92, 246, 0.15) 0%, rgba(236, 72, 153, 0) 50%)',
                  'radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.15) 0%, rgba(236, 72, 153, 0) 50%)'
                ]
              }}
              transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
            />
          </div>

          {/* Floating Decorative Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute w-64 h-64 rounded-full bg-purple-400/10 mix-blend-overlay filter blur-3xl"
              animate={{
                x: ['-5%', '5%', '-5%'],
                y: ['-5%', '5%', '-5%']
              }}
              transition={{ duration: 20, repeat: Infinity, ease: "easeInOut" }}
              style={{ top: '15%', left: '20%' }}
            />
            <motion.div
              className="absolute w-80 h-80 rounded-full bg-indigo-400/10 mix-blend-overlay filter blur-3xl"
              animate={{
                x: ['5%', '-5%', '5%'],
                y: ['5%', '-5%', '5%']
              }}
              transition={{ duration: 25, repeat: Infinity, ease: "easeInOut" }}
              style={{ bottom: '10%', right: '15%' }}
            />
            <motion.div
              className="absolute w-48 h-48 rounded-full bg-pink-400/10 mix-blend-overlay filter blur-3xl"
              animate={{
                x: ['-3%', '3%', '-3%'],
                y: ['3%', '-3%', '3%']
              }}
              transition={{ duration: 15, repeat: Infinity, ease: "easeInOut" }}
              style={{ top: '40%', right: '25%' }}
            />
          </div>

          {/* Subtle Animated Particles */}
          <div className="absolute inset-0">
            <motion.div
              className="absolute h-2 w-2 rounded-full bg-purple-200/40"
              animate={{ opacity: [0.2, 0.8, 0.2] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              style={{ top: '20%', left: '30%' }}
            />
            <motion.div
              className="absolute h-3 w-3 rounded-full bg-purple-200/40"
              animate={{ opacity: [0.2, 0.8, 0.2] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
              style={{ top: '60%', left: '70%' }}
            />
            <motion.div
              className="absolute h-2 w-2 rounded-full bg-purple-200/40"
              animate={{ opacity: [0.2, 0.8, 0.2] }}
              transition={{ duration: 3.5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
              style={{ top: '80%', left: '20%' }}
            />
            <motion.div
              className="absolute h-2 w-2 rounded-full bg-purple-200/40"
              animate={{ opacity: [0.2, 0.8, 0.2] }}
              transition={{ duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 1.5 }}
              style={{ top: '30%', left: '80%' }}
            />
            <motion.div
              className="absolute h-3 w-3 rounded-full bg-purple-200/40"
              animate={{ opacity: [0.2, 0.8, 0.2] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 2 }}
              style={{ top: '50%', left: '50%' }}
            />
          </div>
        </div>

        {/* Content Container - Centered Layout */}
        <div className="relative h-full container mx-auto px-6 z-10 flex flex-col items-center justify-center">
          <div className="text-center max-w-3xl mx-auto">
            {/* Website Name Badge */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8 inline-block"
            >
              <span className="inline-flex items-center px-4 py-1.5 rounded-full bg-purple-600/20 border border-purple-500/30 text-sm font-medium text-purple-200 backdrop-blur-sm">
                <span className="w-2 h-2 rounded-full bg-purple-400 mr-2 animate-pulse"></span>
                BONUS EARNED
              </span>
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.1 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white"
            >
              {heroContent.title}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="text-lg md:text-xl text-purple-100 mb-12 max-w-2xl mx-auto"
            >
              {heroContent.subtitle}
            </motion.p>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.3 }}
            >
              <Link
                to={heroContent.buttonLink}
                className="inline-flex items-center px-8 py-4 rounded-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium shadow-xl hover:shadow-purple-500/20 hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:-translate-y-1"
              >
                {heroContent.buttonText}
                <motion.svg
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="ml-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </motion.svg>
              </Link>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Content container - direct connection to hero section */}
      <div className="bg-gray-50">
        <div className="container mx-auto px-4 py-12 relative z-20">
        {isLoading ? (
          <div className="space-y-8">
            <div>
              <SkeletonLoader type="text" />
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 mt-6">
                <SkeletonLoader type="store" count={12} />
              </div>
            </div>
            <div>
              <SkeletonLoader type="text" />
              <div className="flex gap-4 mt-6 overflow-hidden">
                <SkeletonLoader type="category" count={6} />
              </div>
            </div>
          </div>
        ) : (
          <>


            {/* Featured Stores - Horizontal Scrollable Carousel */}
            {/* Featured Stores Section */}
            <motion.section
              className="mb-16 overflow-hidden"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              {/* Curved background with gradient */}
              <div className="relative bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 rounded-3xl p-8 overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 -translate-y-1/2 translate-x-1/4"></div>
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-indigo-200 to-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-30 translate-y-1/2 -translate-x-1/4"></div>

                {/* Header with different style */}
                <div className="flex justify-between items-center mb-8 relative z-10">
                  <div>
                    <div className="inline-flex items-center px-3 py-1 rounded-full bg-purple-100 border border-purple-200 mb-3">
                      <span className="w-2 h-2 rounded-full bg-purple-500 mr-2"></span>
                      <span className="text-xs font-semibold text-purple-700">FEATURED COLLECTION</span>
                    </div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 bg-clip-text text-transparent">
                      Featured Stores
                    </h2>
                  </div>
                  <Link
                    to="/stores"
                    className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm"
                  >
                    <span>View All</span>
                    <svg
                      className="ml-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>

                {/* Horizontal scrollable carousel with standard sized cards */}
                <div className="relative">
                  <div className="flex items-center">
                    <button
                      onClick={() => {
                        const container = document.querySelector('.featured-stores-carousel');
                        if (container) {
                          container.scrollBy({ left: -600, behavior: 'smooth' });
                        }
                      }}
                      className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-500 shadow-md flex items-center justify-center text-white hover:from-purple-700 hover:to-pink-600 transition-all duration-300 z-10 mr-2 flex-shrink-0"
                      aria-label="Scroll left"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>

                    <motion.div
                      className="flex space-x-4 pb-4 overflow-x-auto scrollbar-hide featured-stores-carousel flex-1"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5 }}
                    >
                      {loadingStates.featuredStores ? (
                        // 骨架屏 - 水平滚动版本
                        Array.from({ length: 8 }).map((_, index) => (
                          <div key={index} className="flex-shrink-0 w-[calc(100%/6-1rem)] min-w-[150px]">
                            <SkeletonLoader type="store" count={1} />
                          </div>
                        ))
                      ) : (
                        featuredStores.map((store) => (
                          <div
                            key={store.id}
                            className="flex-shrink-0 w-[calc(100%/6-1rem)] min-w-[150px] bg-white rounded-xl shadow-sm border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-300"
                          >
                            <StoreCard store={store} />
                          </div>
                        ))
                      )}
                    </motion.div>

                    <button
                      onClick={() => {
                        const container = document.querySelector('.featured-stores-carousel');
                        if (container) {
                          container.scrollBy({ left: 600, behavior: 'smooth' });
                        }
                      }}
                      className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-500 shadow-md flex items-center justify-center text-white hover:from-purple-700 hover:to-pink-600 transition-all duration-300 z-10 ml-2 flex-shrink-0"
                      aria-label="Scroll right"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>


                </div>
              </div>
            </motion.section>

            {/* Categories Section - Modernized */}
            <motion.section
              className="mb-16 py-4"
              style={{ scale: categoriesScale, opacity: categoriesOpacity }}
            >
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center">
                  <motion.div
                    className="w-1.5 h-12 bg-purple-600 rounded-full mr-4"
                    initial={{ height: 0 }}
                    whileInView={{ height: 48 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                  />
                  <motion.h2
                    className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 bg-clip-text text-transparent"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                  >
                    Shop by Category
                  </motion.h2>
                </div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Link
                    to="/stores"
                    className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm"
                  >
                    <span>View All</span>
                    <motion.svg
                      className="ml-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </motion.svg>
                  </Link>
                </motion.div>
              </div>

              <motion.div
                className="relative bg-gradient-to-r from-purple-50 via-white to-pink-50 rounded-2xl py-8 px-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                {/* 背景装饰元素 */}
                <div className="absolute inset-0 overflow-hidden opacity-10 pointer-events-none">
                  <div className="absolute top-0 left-1/4 w-64 h-64 bg-gradient-to-br from-purple-300 to-pink-400 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
                  <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
                </div>

                {/* 分类网格 */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 relative z-10">
                  {loadingStates.categories ? (
                    // 骨架屏 - 分类网格
                    Array.from({ length: 18 }).map((_, index) => (
                      <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                        <SkeletonLoader type="category" count={1} />
                      </div>
                    ))
                  ) : (
                    categories.slice(0, 18).map((category, index) => (
                      <motion.div
                        key={`${category.id}-${index}`}
                        className="bg-white rounded-xl shadow-sm border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-300 transform-gpu"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.4, delay: 0.05 * (index % 6) }}
                        whileHover={{
                          y: -5,
                          backgroundColor: "#fff",
                          boxShadow: "0 10px 25px rgba(147, 51, 234, 0.1)"
                        }}
                      >
                        <div className="p-4">
                        <CategoryCard
                          id={category.id}
                          name={category.name}
                          icon={renderCategoryIcon(category)}
                          index={index}
                        />
                      </div>
                      </motion.div>
                    ))
                  )}
                </div>

                {/* 底部装饰波浪 */}
                <div className="absolute bottom-0 left-0 w-full h-8 overflow-hidden leading-0">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none" className="relative block w-full h-8 text-white">
                    <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" className="fill-current opacity-20"></path>
                  </svg>
                </div>
                </motion.div>
            </motion.section>

            {/* Popular Stores - Title on Left, Uniform Cards */}
            <motion.section className="mb-16">
              <div className="relative bg-gradient-to-r from-purple-50 via-white to-pink-50 rounded-3xl p-6 md:p-8 overflow-hidden">
                {/* Background decorative elements */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20"></div>

                {/* Grid layout with title on left */}
                <div className="grid grid-cols-1 md:grid-cols-6 gap-8">
                  {/* Left side - Title column */}
                  <div className="md:col-span-1 flex flex-col justify-start">
                    <div className="flex items-center mb-4">
                      <div className="w-1.5 h-12 bg-purple-600 rounded-full mr-4"></div>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 bg-clip-text text-transparent whitespace-nowrap">Popular Stores</h2>
                    </div>

                    <p className="text-gray-600 mb-6 hidden md:block">Discover the most popular stores with the best cashback offers</p>

                    <Link
                      to="/stores"
                      className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm hidden md:flex"
                    >
                      <span>View All</span>
                      <svg
                        className="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>
                  </div>

                  {/* Right side - Stores grid */}
                  <div className="md:col-span-5">
                    {/* Uniform grid of stores - 5 per row with increased spacing */}
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-6">
                      {loadingStates.popularStores ? (
                        // 骨架屏 - 商店网格
                        Array.from({ length: 10 }).map((_, index) => (
                          <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-100">
                            <SkeletonLoader type="store" count={1} />
                          </div>
                        ))
                      ) : (
                        popularStores.slice(0, 30).map((store) => (
                          <div
                            key={store.id}
                            className="bg-white rounded-xl shadow-sm border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-300"
                          >
                            <StoreCard store={store} />
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  {/* Mobile view all link */}
                  <div className="md:hidden text-center mt-6">
                    <Link
                      to="/stores"
                      className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm"
                    >
                      <span>View All Popular Stores</span>
                      <svg
                        className="ml-2 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.section>

            {/* Travel & Transportation Section - Title on Top, Uniform Cards */}
            <motion.section className="mb-16">
              <div className="relative bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-3xl p-6 md:p-8 overflow-hidden">
                {/* Background decorative elements */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-64 h-64 bg-indigo-100 rounded-full mix-blend-multiply filter blur-3xl opacity-20 pointer-events-none"></div>

                {/* Header row with title and view all link */}
                <div className="flex flex-wrap justify-between items-center mb-8 relative z-10">
                  <div className="flex items-center">
                    <div className="w-1.5 h-12 bg-purple-600 rounded-full mr-4"></div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 bg-clip-text text-transparent">
                      Travel & Transportation
                    </h2>
                  </div>

                  <Link
                    to="/stores?category_id=53"
                    className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm relative z-20"
                  >
                    <span>View All</span>
                    <motion.svg
                      className="ml-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </motion.svg>
                  </Link>
                </div>

                {/* Uniform grid of travel stores - 6 per row */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 relative z-10">
                  {travelStores.map((store) => (
                    <div
                      key={store.id}
                      className="bg-white rounded-xl shadow-sm border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-300"
                    >
                      <StoreCard store={store} />
                    </div>
                  ))}
                </div>
              </div>
            </motion.section>

            {/* Coupons Section - New */}
            <motion.section className="mb-16">
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center">
                  <motion.div
                    className="w-1.5 h-12 bg-purple-600 rounded-full mr-4"
                    initial={{ height: 0 }}
                    whileInView={{ height: 48 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                  />
                  <motion.h2
                    className="text-3xl font-bold bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 bg-clip-text text-transparent"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5 }}
                  >
                    Latest Coupons
                  </motion.h2>
                </div>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Link
                    to="/coupons"
                    className="inline-flex items-center px-5 py-2 rounded-full bg-purple-600 text-white font-medium hover:bg-purple-700 transition-colors duration-300 shadow-sm"
                  >
                    <span>View All</span>
                    <motion.svg
                      className="ml-2 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </motion.svg>
                  </Link>
                </motion.div>
              </div>

              {featuredCoupons.length > 0 ? (
                <motion.div
                  className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  {featuredCoupons.map((coupon, index) => (
                    <motion.div
                      key={`${coupon.id}-${index}`}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.1 + (index % 5) * 0.1 }}
                    >
                      <CouponCard
                        id={coupon.id}
                        merchantId={coupon.merchant_id}
                        merchantName={coupon.merchant_name || ''}
                        merchantLogo={coupon.merchant_info?.logo || coupon.merchant_logo || ''}
                        merchantUniqueName={(coupon.merchant_info?.unique_name || coupon.merchant_unique_name || '') as string}
                        title={coupon.title || ''}
                        code={coupon.code || ''}
                        description={coupon.description || ''}
                        endedAt={coupon.ended_at}
                        merchantTrackUrl={coupon.merchant_track_url}
                        onClick={() => handleCouponClick(coupon)}
                      />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="bg-gray-50 rounded-xl p-8 text-center">
                  <div className="text-xl font-medium text-gray-500 mb-4">No coupons available at the moment</div>
                  <p className="text-gray-400 mb-6">Check back later for exclusive deals and discounts</p>
                  <Link
                    to="/stores"
                    className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                  >
                    Browse Stores Instead
                  </Link>
                </div>
              )}
            </motion.section>

          </>
        )}
        </div>
      </div>

      {/* 回到顶部按钮 - 增强 */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.5 }}
            whileHover={{ scale: 1.1, boxShadow: "0 10px 25px -5px rgba(147, 51, 234, 0.4)" }}
            whileTap={{ scale: 0.9 }}
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 p-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg hover:from-purple-700 hover:to-pink-700 focus:outline-none z-50"
            aria-label="Scroll to top"
          >
            <motion.div
              animate={{
                y: [2, -2, 2]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
          >
            <FaArrowUp className="w-6 h-6" />
            </motion.div>
          </motion.button>
        )}
      </AnimatePresence>
    </main>
  )
}

export default Home
