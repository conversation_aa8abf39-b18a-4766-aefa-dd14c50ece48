import React, { useState, useRef, useEffect } from 'react'
import { useCountry } from '../contexts/CountryContext'
import { FaChevronDown } from 'react-icons/fa'

interface CountrySelectorProps {
  className?: string
}

const CountrySelector: React.FC<CountrySelectorProps> = ({ className = '' }) => {
  // 添加错误边界处理
  let selectedCountry, countries, setSelectedCountry, loading

  try {
    const countryContext = useCountry()
    selectedCountry = countryContext.selectedCountry
    countries = countryContext.countries
    setSelectedCountry = countryContext.setSelectedCountry
    loading = countryContext.loading
  } catch (error) {
    console.warn('CountrySelector: useCountry hook failed, using fallback', error)
    // 使用fallback值
    selectedCountry = {
      id: 1,
      name: 'United States',
      code: 'US',
      flag: '🇺🇸',
      currency: 'USD',
      status: 1
    }
    countries = [selectedCountry]
    setSelectedCountry = () => {}
    loading = false
  }
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleCountrySelect = (country: any) => {
    setSelectedCountry(country)
    setIsOpen(false)
  }

  if (loading || !selectedCountry) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-6 h-4 bg-gray-200 rounded animate-pulse"></div>
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
      >
        <span className="text-lg">{selectedCountry.flag}</span>
        <span className="hidden sm:inline">{selectedCountry.name}</span>
        <span className="sm:hidden">{selectedCountry.code}</span>
        <FaChevronDown
          className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
          <div className="py-1">
            {countries.map((country) => (
              <button
                key={country.id}
                onClick={() => handleCountrySelect(country)}
                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center space-x-3 ${
                  selectedCountry.id === country.id ? 'bg-purple-50 text-purple-700' : 'text-gray-700'
                }`}
              >
                <span className="text-lg">{country.flag}</span>
                <div className="flex-1">
                  <div className="font-medium">{country.name}</div>
                  <div className="text-xs text-gray-500">{country.code} • {country.currency}</div>
                </div>
                {selectedCountry.id === country.id && (
                  <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default CountrySelector
