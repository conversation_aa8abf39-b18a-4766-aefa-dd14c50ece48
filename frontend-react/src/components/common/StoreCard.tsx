import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Store } from '../../types'

interface StoreCardProps {
  store: Store
  className?: string
}

const StoreCard: React.FC<StoreCardProps> = ({ store, className = '' }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Detect device type
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  const getCashbackText = () => {
    return `${store.cashback_value} Cashback`;
  }

  const cashbackText = getCashbackText()

  return (
    <motion.div
      className={`relative bg-white rounded-xl overflow-hidden h-full shadow-sm hover:shadow-md transition-all duration-300 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ aspectRatio: "0.85/1" }} // Slightly adjusted aspect ratio for better proportions
    >
      {/* Country flag - positioned at top-right corner */}
      {store.country && (
        <div className="absolute top-1 right-1 z-10">
          <span className="inline-flex items-center px-1.5 py-0.5 text-[10px] font-medium text-purple-700 bg-purple-50 rounded-md shadow-sm">
            {store.country}
          </span>
        </div>
      )}

      <div className="flex flex-col h-full">
        {/* Store logo - top section - reduced height */}
        <Link to={`/stores/${store.unique_name}`} className="block h-[60%] flex items-center justify-center bg-gradient-to-b from-white to-gray-50 p-2">
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Hover effect */}
            <div className="absolute inset-0 bg-gradient-to-b from-purple-50/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />

            {store.logo && store.logo !== '' ? (
              <img
                src={store.logo}
                alt={store.name}
                className="max-w-[75%] max-h-[75%] object-contain filter drop-shadow-sm hover:scale-105 transition-transform duration-300"
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.parentElement?.classList.add('logo-error');
                }}
              />
            ) : (
              <div
                className="w-14 h-14 rounded-full bg-gradient-to-br from-purple-400 to-pink-600 flex items-center justify-center shadow-md hover:scale-105 transition-transform duration-300"
              >
                <span className="text-lg font-bold text-white">
                  {store.name.charAt(0)}
                </span>
              </div>
            )}
          </div>
        </Link>

        {/* Store information - bottom section - increased height */}
        <div className="p-2 flex flex-col items-center justify-center relative h-[40%]">
          <Link to={`/stores/${store.unique_name}`} className="block text-center w-full">
            <h3
              className="text-xs font-semibold text-gray-800 mb-1 line-clamp-1 hover:text-purple-600 transition-colors duration-300"
              title={store.name}
            >
              {store.name}
            </h3>
          </Link>

          {cashbackText && (
            <div className="text-center">
              <div className="bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-400 bg-clip-text text-transparent font-medium text-xs py-0.5 px-2 rounded-full inline-block">
                {cashbackText}
              </div>
            </div>
          )}

          {/* Shop Now button - only visible on hover - increased size */}
          <div
            className={`absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 transition-opacity duration-300 ${isHovered || isMobile ? 'opacity-100' : 'opacity-0'}`}
            style={{ pointerEvents: isHovered || isMobile ? 'auto' : 'none' }}
          >
            <a
              href={store.track_url}
              target="_blank"
              rel="noopener noreferrer"
              className="relative flex items-center justify-center w-4/5 text-xs font-semibold text-white bg-gradient-to-r from-purple-500 to-pink-400 rounded-md py-2 hover:from-purple-600 hover:to-pink-500 hover:scale-105 transition-all duration-300 touch-manipulation shadow-md"
              style={{
                minHeight: isMobile ? '34px' : '30px',
                WebkitTapHighlightColor: 'transparent'
              }}
            >
              <span className="flex items-center">
                Shop Now
                <svg
                  className="ml-1.5 w-3.5 h-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </span>
            </a>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default StoreCard
